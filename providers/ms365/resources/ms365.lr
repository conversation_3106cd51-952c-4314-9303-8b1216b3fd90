// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

option provider = "go.mondoo.com/cnquery/v9/providers/ms365"
option go_package = "go.mondoo.com/cnquery/v11/providers/ms365/resources"

alias microsoft.organization = microsoft.tenant

// Microsoft
microsoft {
  // Deprecated: use `microsoft.tenant` instead
  organizations() []microsoft.tenant
  // List of users
  users() microsoft.users
  // List of groups
  groups() microsoft.groups
  // List of domains
  domains() []microsoft.domain
  // List of applications
  applications() microsoft.applications
  // List of service principals
  serviceprincipals() []microsoft.serviceprincipal
  // List of enterprise applications
  enterpriseApplications() []microsoft.serviceprincipal
  // List of roles
  roles() microsoft.roles
  // Microsoft 365 settings
  settings() dict
  // The connected tenant's default domain name
  tenantDomainName() string
  // Identity and Access policies
  identityAndAccess() microsoft.identityAndAccess
}

// Microsoft groups
microsoft.groups {
  []microsoft.group
  // Total number of Microsoft groups
  length() int
}

// List of Microsoft Entra ID application registrations
microsoft.applications {
  []microsoft.application
  // Total number of application registrations
  length() int
}

//  Microsoft Entra tenant
microsoft.tenant @defaults("name") {
  // Organization ID
  id string
  // Service plans associated with the tenant
  assignedPlans []dict
  // Provisioned plan
  provisionedPlans []dict
  // Deprecated: use `createdAt` instead
  createdDateTime time
  // Deprecated: use `name` instead
  displayName string
  // Tenant display name
  name string
  // Organization verified domains
  verifiedDomains []dict
  // Whether password hash sync is enabled for hybrid deployments
  onPremisesSyncEnabled bool
  // Tenant creation date
  createdAt time
  // Tenant type
  type string
  // Commercial subscription
  subscriptions() []dict
  // Company-wide settings for apps and services.
  settings() microsoft.tenant.settings
  // Company-wide settings for Microsoft Forms
  formsSettings() microsoft.tenant.formsSettings
  // Privacy profile data with contact email and statement URL
  privacyProfile dict
  // List of technical notification mails
  technicalNotificationMails []string
  // Preferred language
  preferredLanguage string
}

// Company-wide configuration for apps and services.
private microsoft.tenant.settings @defaults("isAppAndServicesTrialEnabled isOfficeStoreEnabled") {
  // Controls whether users can start trial subscriptions for apps and services in your organization.
  isAppAndServicesTrialEnabled bool
  // Controls whether users can access the Microsoft Store
  isOfficeStoreEnabled bool
}

// Company-wide settings for Microsoft Forms
private microsoft.tenant.formsSettings @defaults("isExternalSendFormEnabled isBingImageSearchEnabled") {
  // Controls whether users can send a link to a form to an external user
  isExternalSendFormEnabled bool
  // Controls whether users can collaborate on a form layout and structure with an external user
  isExternalShareCollaborationEnabled bool
  // Controls whether users can share form results with external users
  isExternalShareResultEnabled bool
  // Controls whether users can share form templates with external users
  isExternalShareTemplateEnabled bool
  // Controls whether the names of users who fill out forms are recorded
  isRecordIdentityByDefaultEnabled bool
  // Controls whether users can add images from Bing to forms
  isBingImageSearchEnabled bool
  // Controls whether phishing protection is run on forms created by users
  isInOrgFormsPhishingScanEnabled bool
}

// List of Microsoft Entra users with optional filters
microsoft.users {
  []microsoft.user

  init(filter? string, search? string)
  // Filter users by property values
  filter string
  // Search users by search phrases
  search string
}

// A container resource for identity and access policies that can be filtered
microsoft.identityAndAccess {
  []microsoft.identityAndAccess.policy
  init(filter? string)
  // filter by scopeId and scopeType
  // scopeId eq '/' and scopeType eq 'Directory'
  filter string

  // Get the instances of role eligibilities
  roleEligibilityScheduleInstances() []microsoft.identityAndAccess.roleEligibilityScheduleInstance
}

// Represents an instance of a role eligibility in PIM
private microsoft.identityAndAccess.roleEligibilityScheduleInstance @defaults("memberType principalId roleDefinitionId") {
  // The unique identifier of the role eligibility schedule instance
  id string
  // The identifier of the principal (user, group, or service principal) that has the role eligibility
  principalId string
  // The identifier of the role definition that the eligibility is for
  roleDefinitionId string
  // The identifier of the directory object representing the scope of the role eligibility
  directoryScopeId string
  // The identifier of the app-specific scope of the role eligibility
  appScopeId string
  // The start time of the role eligibility
  startDateTime time
  // The end time of the role eligibility
  endDateTime time
  // The type of member, either 'Direct' or 'Inherited'.
  memberType string
  // The identifier of the role eligibility schedule from which this instance is created
  roleEligibilityScheduleId string
}

// A PIM role management policy for Microsoft Entra ID roles
private microsoft.identityAndAccess.policy @defaults("id displayName scopeType") {
  // Policy ID
  id string
  // Policy display name
  displayName string
  // Policy description
  description string
  // True if this policy is the default organization policy
  isOrganizationDefault bool
  // The identifier of the scope where the policy is defined
  scopeId string
  // The type of the scope where the policy is defined
  scopeType string
  // The time when this policy was last modified
  lastModifiedDateTime time
  // The identity of the user who last modified the policy
  lastModifiedBy dict
  // The collection of rules that are part of the policy
  rules() []microsoft.identityAndAccess.policy.rule
}

// A rule defined for a PIM role management policy
private microsoft.identityAndAccess.policy.rule @defaults("id") {
  // The unique identifier for the rule
  id string
  // Defines details of scope that's targeted by role management policy rule
  target microsoft.identityAndAccess.policy.rule.target
}

// Defines details of the scope that's targeted by role management policy rule
private microsoft.identityAndAccess.policy.rule.target {
  // The type of caller that's the target of the policy rule. Allowed values are: None, Admin, EndUser
  caller string
  // The list of role settings that are enforced and cannot be overridden by child scopes
  enforcedSettings []string
  // The list of role settings that can be inherited by child scopes
  inheritableSettings []string
  // The role assignment type that's the target of policy rule
  level string
  // The role management operations that are the target of the policy rule
  operations []string
}

// A single license assigned to a user
private microsoft.user.assignedLicense @defaults("skuId") {
  // A collection of the unique identifiers for plans that have been disabled.
  disabledPlans []string
  // The unique identifier for the SKU.
  skuId string
}

// Details of a single license assigned to a user
private microsoft.user.licenseDetail @defaults("skuId skuPartNumber") {
  // The unique identifier for the license detail object
  id string
  // The unique identifier for the SKU.
  skuId string
  // The unique, friendly name for the product.
  skuPartNumber string
  // A collection of the service plans that are provided with the SKU.
  servicePlans []microsoft.user.licenseDetail.servicePlanInfo
}

// Contains information about a service plan associated with a subscribed SKU
private microsoft.user.licenseDetail.servicePlanInfo @defaults("appliesTo provisioningStatus servicePlanName") {
  // The object the service plan can be assigned to. The possible values are: User or Company
  appliesTo string
  // The provisioning status of the service plan. The possible values are:
  // Success, Disabled, Error, PendingInput, PendingActivation, PendingProvisioning
  provisioningStatus string
  // The unique identifier of the service plan
  servicePlanId string
  // The name of the service plan.
  servicePlanName string
}

// Microsoft Entra Conditional Access Policies
microsoft.conditionalAccess {
  // Named locations container
  namedLocations microsoft.conditionalAccess.namedLocations
  // Policies collection
  policies() []microsoft.conditionalAccess.policy
  // Tenant-wide authentication methods policy
  authenticationMethodsPolicy() microsoft.conditionalAccess.authenticationMethodsPolicy
}

// The tenant-wide policy that controls which authentication methods are allowed
private microsoft.conditionalAccess.authenticationMethodsPolicy @defaults("id displayName") {
  // The identifier of the policy
  id string
  // The name of the policy
  displayName string
  // A description of the policy
  description string
  // The date and time of the last update to the policy
  lastModifiedDateTime time
  // The version of the policy in use
  policyVersion string
  // Represents the settings for each authentication method
  authenticationMethodConfigurations []microsoft.conditionalAccess.authenticationMethodConfiguration
}

// Configuration for a specific authentication method
private microsoft.conditionalAccess.authenticationMethodConfiguration @defaults("id state") {
  // The policy name
  id string
  // The policy name
  state string
}

// Container for Microsoft Entra Conditional Access Named Locations
microsoft.conditionalAccess.namedLocations {
  // IP-based named locations
  ipLocations() []microsoft.conditionalAccess.ipNamedLocation
  // Country-based named locations
  countryLocations() []microsoft.conditionalAccess.countryNamedLocation
}

// A Microsoft Entra Conditional Access policy. Conditional access policies are custom rules that define an access scenario.
microsoft.conditionalAccess.policy @defaults("id displayName state") {
  // Specifies the identifier of a conditionalAccessPolicy object
  id string
  // Specifies a display name for the conditionalAccessPolicy object
  displayName string
  // Specifies the state of the conditionalAccessPolicy object. Possible values are: enabled, disabled, enabledForReportingButNotEnforced.
  state string
  // The Timestamp type represents date and time information using ISO 8601 format and is always in UTC time
  createdDateTime time
  // The Timestamp type represents date and time information using ISO 8601 format and is always in UTC time
  modifiedDateTime time
  // Policy conditions
  conditions microsoft.conditionalAccess.policy.conditions
  // Specifies the grant controls that must be fulfilled to pass the policy
  grantControls microsoft.conditionalAccess.policy.grantControls
  // Specifies the session controls that are enforced after sign-in
  sessionControls microsoft.conditionalAccess.policy.sessionControls
  // Template ID (if created from template)
  templateId string
}

// Represents the type of conditions that govern when the policy applies
private microsoft.conditionalAccess.policy.conditions @defaults("id applications platforms") {
  // Internal ID based on policy ID
  id string
  // Applications and user actions included in and excluded from the policy
  applications microsoft.conditionalAccess.policy.conditions.applications
  // Authentication flows included in the policy scope
  authenticationFlows microsoft.conditionalAccess.policy.conditions.authenticationFlows
  // Client applications (service principals and workload identities) included in and excluded from the policy. Either users or clientApplications is required.
  clientApplications microsoft.conditionalAccess.policy.conditions.clientApplications
  // Client application types included in the policy
  clientAppTypes []string
  // Locations included in and excluded from the policy
  locations microsoft.conditionalAccess.policy.conditions.locations
  // Platforms included in and excluded from the policy
  platforms microsoft.conditionalAccess.policy.conditions.platforms
  // Service principal risk levels included in the policy. Possible values are: low, medium, high, none, unknownFutureValue.
  servicePrincipalRiskLevels []string
  // Sign-in risk levels included in the policy. Possible values are: low, medium, high, hidden, none, unknownFutureValue.
  signInRiskLevels []string
  // User risk levels included in the policy. Possible values are: low, medium, high, hidden, none, unknownFutureValue.
  userRiskLevels []string
  // Users, groups, and roles included in and excluded from the policy. Either users or clientApplications is required.
  users microsoft.conditionalAccess.policy.conditions.users
  // Insider risk levels included in the policy. The possible values are: minor, moderate, elevated, unknownFutureValue.
  insiderRiskLevels string
}

// The authentication flows in scope for a Microsoft Entra Conditional Access policy
private microsoft.conditionalAccess.policy.conditions.authenticationFlows @defaults("transferMethods") {
  // Represents the transfer methods in scope for the policy. The possible values are: none, deviceCodeFlow, authenticationTransfer, unknownFutureValue.
  transferMethods string
}

// A collection of settings that define specific combinations of authentication methods and metadata
private microsoft.conditionalAccess.policy.grantControls.authenticationStrength @defaults("id displayName allowedCombinations") {
  // The system-generated identifier for this mode
  id string
  // A collection of authentication method modes that are required be used to satisfy this authentication strength
  allowedCombinations []string
  // The human-readable display name of this policy
  displayName string
  // The human-readable description of this policy
  description string
  // A descriptor if this policy is built into Microsoft Entra ID or created by an admin for the tenant. The possible values are: builtIn, custom, unknownFutureValue.
  policyType string
  // A descriptor if this authentication strength grants the MFA claim upon successful satisfaction. The possible values are: none, mfa, unknownFutureValue.
  requirementsSatisfied string
  // The time when this policy was created
  createdDateTime time
  // The time when this policy was last modified
  modifiedDateTime time
}

// Session control to enforce sign-in frequency
private microsoft.conditionalAccess.policy.sessionControls.signInFrequency @defaults("authenticationType isEnabled") {
  // The possible values are primaryAndSecondaryAuthentication, secondaryAuthentication, unknownFutureValue.
  authenticationType string
  // The possible values are timeBased, everyTime, unknownFutureValue. Sign-in frequency of everyTime is available for risky users, risky sign-ins, and Intune device enrollment.
  frequencyInterval string
  // Specifies whether the session control is enabled.
  isEnabled bool
}

// Session control used to enforce cloud app security checks
private microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity @defaults("cloudAppSecurityType isEnabled") {
  // Specifies CloudApp security session control types
  cloudAppSecurityType string
  // Specifies whether the session control is enabled
  isEnabled bool
}

// Session control to define whether to persist cookies or not
private microsoft.conditionalAccess.policy.sessionControls.persistentBrowser @defaults("mode isEnabled") {
  // Possible values are: always, never
  mode string
  // Specifies whether the session control is enabled
  isEnabled bool
}

// Session control to enforce application restrictions
private microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions @defaults("isEnabled") {
  // Specifies whether the session control is enabled or not
  isEnabled bool
}

// Represents client applications (service principals and workload identities) included in and excluded from the policy scope
private microsoft.conditionalAccess.policy.conditions.clientApplications @defaults("excludeServicePrincipals includeServicePrincipals") {
  // Service principal IDs excluded from the policy scope
  excludeServicePrincipals []string
  // Service principal IDs included in the policy scope, or ServicePrincipalsInMyTenant
  includeServicePrincipals []string
}

// Platforms included in and excluded from the policy scope
private microsoft.conditionalAccess.policy.conditions.platforms @defaults("excludePlatforms includePlatforms") {
  // Possible values are: android, iOS, windows, windowsPhone, macOS, linux, all, unknownFutureValue
  excludePlatforms []string
  // Possible values are: android, iOS, windows, windowsPhone, macOS, linux, all, unknownFutureValue
  includePlatforms []string
}

// Represents the applications and user actions included in and excluded from the conditional access policy
private microsoft.conditionalAccess.policy.conditions.applications @defaults("includeApplications excludeApplications includeUserActions") {
  // Can be one of the following: appId, All, Office365, MicrosoftAdminPortals
  includeApplications []string
  // Can be one of the following: appId, All, Office365, MicrosoftAdminPortals
  excludeApplications []string
  // User actions to include
  includeUserActions []string
}

// Users, groups, and roles included in and excluded from a Microsoft Entra Conditional Access policy scope
private microsoft.conditionalAccess.policy.conditions.users @defaults("includeUsers excludeUsers") {
  // User IDs in scope of policy unless explicitly excluded, None, All, or GuestsOrExternalUsers
  includeUsers []string
  // User IDs excluded from scope of policy and/or GuestsOrExternalUsers
  excludeUsers []string
  // Group IDs in scope of policy unless explicitly excluded
  includeGroups []string
  // Group IDs excluded from scope of policy
  excludeGroups []string
  // Role IDs in scope of policy unless explicitly excluded
  includeRoles []string
  // Role IDs excluded from scope of policy
  excludeRoles []string
}

// Locations included in and excluded from the scope of a Microsoft Entra Conditional Access policy. Locations can be countries and regions or IP addresses.
private microsoft.conditionalAccess.policy.conditions.locations @defaults("includeLocations excludeLocations") {
  // Location IDs in scope of policy unless explicitly excluded, All, or AllTrusted
  includeLocations []string
  // Location IDs excluded from scope of policy
  excludeLocations []string
}

// Represents grant controls that must be fulfilled to pass the policy
private microsoft.conditionalAccess.policy.grantControls @defaults("id") {
  // Internal ID based on policy ID
  id string
  // Defines the relationship of the grant controls. Possible values: AND, OR.
  operator string
  // List of values of built-in controls required by the policy. Possible values: block, mfa, compliantDevice, domainJoinedDevice, approvedApplication, compliantApplication, passwordChange, unknownFutureValue.
  builtInControls []string
  // The authentication strength required by the conditional access policy (Optional)
  authenticationStrength microsoft.conditionalAccess.policy.grantControls.authenticationStrength
  // List of custom controls IDs required by the policy
  customAuthenticationFactors []string
  // List of terms of use IDs required by the policy
  termsOfUse []string
}

// Microsoft Entra Conditional Access Policy Session Controls
private microsoft.conditionalAccess.policy.sessionControls @defaults("id") {
  // Internal ID based on policy ID
  id string
  // Session control to enforce signin frequency
  signInFrequency microsoft.conditionalAccess.policy.sessionControls.signInFrequency
  // Session control to apply cloud app security
  cloudAppSecurity microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity
  // Session control to define whether to persist cookies or not. All apps should be selected for this session control to work correctly.
  persistentBrowser dict
  // Session control to enforce application restrictions. Only Exchange Online and SharePoint Online support this session control
  applicationEnforcedRestrictions microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions
  // Secure application model for continuous access evaluation
  secureSignInSession dict
}

// Microsoft Entra Conditional Access IP named location
microsoft.conditionalAccess.ipNamedLocation @defaults("name trusted") {
  // Named location name
  name string
  // Whether the location is marked as trusted
  trusted bool
}

// Microsoft Entra Conditional Access Country named location
microsoft.conditionalAccess.countryNamedLocation @defaults("name lookupMethod") {
  // Named location name
  name string
  // Method to determine the country location
  lookupMethod string
}

// Microsoft Entra ID user
private microsoft.user @defaults("id displayName userPrincipalName") {
  // User Object ID
  id string
  // Whether the user account is enabled
  accountEnabled bool
  // User city
  city string
  // Deprecated: use job.companyName instead
  companyName string
  // Deprecated: use contact.country instead
  country string
  // User create time
  createdDateTime time
  // Deprecated: use job.department instead
  department string
  // User display name
  displayName string
  // Deprecated: use job.employeeId instead
  employeeId string
  // User given name
  givenName string
  // Deprecated: use job.title instead
  jobTitle string
  // Deprecated: use contact.email instead
  mail string
  // Deprecated: use contact.mobilePhone instead
  mobilePhone string
  // Deprecated: use contact.otherMails instead
  otherMails []string
  // Deprecated: use job.officeLocation instead
  officeLocation string
  // Deprecated: use contact.postalCode instead
  postalCode string
  // Deprecated: use contact.state instead
  state string
  // Deprecated: use contact.streetAddress instead
  streetAddress string
  // User surname
  surname string
  // User service principal name
  userPrincipalName string
  // User type
  userType string
  // User settings
  settings() dict
  // Job information
  job() dict
  // Contact information
  contact() dict
  // Authentication information
  authMethods() microsoft.user.authenticationMethods
  // Whether MFA is enabled for the user
  mfaEnabled() bool
  // The user's creation type
  creationType string
  // The user's identities
  identities []microsoft.user.identity
  // The user's audit log
  auditlog() microsoft.user.auditlog
  // The licenses that are assigned to the user, including inherited (group-based) licenses
  assignedLicenses []microsoft.user.assignedLicense
  // A collection of this user's license details
  licenseDetails() []microsoft.user.licenseDetail
  // Authentication requirements information
  authenticationRequirements() microsoft.user.authenticationRequirements
}

// Microsoft user authentication method states
private microsoft.user.authenticationRequirements {
  // user's MFA state
  perUserMfaState string
}

// Microsoft user audit log
private microsoft.user.auditlog {
  // The user's ID
  userId string
  // The user's interactive sign-in entries (a maximum of 50 entries from the last 24 hours only)
  signins() []microsoft.user.signin
  // The user's last interactive sign-in
  lastInteractiveSignIn() microsoft.user.signin
  // The user's last non-interactive sign-in (from the last 24 hours only)
  lastNonInteractiveSignIn() microsoft.user.signin
}

// Microsoft user identity
private microsoft.user.identity @defaults("issuerAssignedId") {
  // The identity as assigned by the issuer
  issuerAssignedId string
  // The identity issuer
  issuer string
  // The sign-in type for the identity (e.g., 'federated' or 'userPrincipalName')
  signInType string
}

// Microsoft user sign-in
private microsoft.user.signin {
  // The sign-in entry's identifier
  id string
  // The date and time the sign-in entry was created
  createdDateTime time
  // The ID of the user
  userId string
  // The display name of the user
  userDisplayName string
  // The client app used to perform the sign-in
  clientAppUsed string
  // The display name of the client app used to perform the sign-in
  appDisplayName string
  // The resource's display name
  resourceDisplayName string
  // Whether the sign-in was interactive
  interactive bool
}

// Microsoft Entra authentication methods
private microsoft.user.authenticationMethods @defaults("count") {
    // Count of authentication methods
    count int
    // Phone number and type registered to a user
    phoneMethods []dict
    // Email authentication method for self-service password reset (SSPR)
    emailMethods []dict
    // FIDO2 security key registered to a user
    fido2Methods []dict
    // Software OATH token registered to a user
    softwareMethods []dict
    // Microsoft Authenticator app registered to a user
    microsoftAuthenticator []dict
    // User password authentication method
    passwordMethods []dict
    // Temporary Access Pass registered to a user
    temporaryAccessPassMethods []dict
    // Windows Hello for Business authentication method registered to a user
    windowsHelloMethods []dict
    // Retrieves the registration and capability status for a user's authentication methods
    registrationDetails() microsoft.user.authenticationMethods.userRegistrationDetails
}

// Represents the state of a user's authentication methods, including which methods are registered and capable
private microsoft.user.authenticationMethods.userRegistrationDetails @defaults("id userDisplayName")  {
  // User's object identifier in Microsoft Entra ID
  id string
  // True if the user has an administrator role in the tenant
  isAdmin bool
  // True if the user has registered a strong auth method that is enabled by the multifactor authentication policy
  isMfaCapable bool
  // True if the user has registered at least one strong authentication method, regardless of whether it is enabled by policy
  isMfaRegistered bool
  // True if the user has registered a strong authentication method that doesn't require a password and is enabled by policy
  isPasswordlessCapable bool
  // True if the user is capable of self-service password reset by having registered the required number of methods and being included in the policy
  isSsprCapable bool
  // True if the user is allowed to perform self-service password reset by policy, even if they have not yet registered the required number of methods
  isSsprEnabled bool
  // True if the user has registered the required number of authentication methods for self-service password reset, regardless of whether they are enabled for by policy
  isSsprRegistered bool
  // True if system-preferred authentication is enabled, which allows the system to dynamically determine the most secure authentication method for the user
  isSystemPreferredAuthenticationMethodEnabled bool
  // The date and time when the user's registration details were last updated
  lastUpdatedDateTime time
  // Collection of authentication methods registered by the user, such as "mobilePhone", "email", and "fido2SecurityKey"
  methodsRegistered []string
  // Collection of authentication methods that the system determined to be the most secure for the user to perform multifactor authentication
  systemPreferredAuthenticationMethods []string
  // The user's display name
  userDisplayName string
  // The method the user selected as their default for second-factor authentication. This is used when system-preferred authentication is disabled.
  userPreferredMethodForSecondaryAuthentication string
  // The user's principal name
  userPrincipalName string
  // The user's type, which can be "member", "guest", or "unknownFutureValue"
  userType string
}

// Microsoft group
private microsoft.group @defaults("id displayName") {
  // Group ID
  id string
  // Group display name
  displayName string
  // Whether group security is enabled
  securityEnabled bool
  // Whether group email is enabled status
  mailEnabled bool
  // Group email nickname
  mailNickname string
  // Group email
  mail string
  // Group visibility state
  visibility string
  // List of group members
  members() []microsoft.user
  // Group types indicating the membership and classification of the group
  groupTypes []string
  // Membership rule used for dynamic group membership
  membershipRule string
  // State of the processing for the dynamic membership rule
  membershipRuleProcessingState string
}

// List of Microsoft Entra devices
microsoft.devices {
  []microsoft.device

  init(filter? string, search? string)
  // Filter devices by property values
  filter string
  // Search devices by search phrases
  search string
}

// Microsoft device
private microsoft.device @defaults("id displayName") {
  // Device ID
  id string
  // Device display name
  displayName string
  // Unique identifier set
  deviceId string
  // User-defined property set by Intune
  deviceCategory string
  // Enrollment profile applied to the device
  enrollmentProfileName string
  // Enrollment type of the device
  enrollmentType string
  // Whether the device complies with Mobile Device Management (MDM) policies
  isCompliant bool
  // Whether the device is managed by a Mobile Device Management (MDM) app
  isManaged bool
  // Manufacturer
  manufacturer string
  // Whether the device is rooted or jail-broken
  isRooted bool
  // Application identifier used to register device into MDM
  mdmAppId string
  // Model of the device
  model string
  // The type of operating system on the device
  operatingSystem string
  // The version of the operating system on the device
  operatingSystemVersion string
  // Physical IDs
  physicalIds []string
  // Date and time of when the device was registered
  registrationDateTime time
  // List of labels applied to the device by the system
  systemLabels []string
  // Type of trust for the joined device
  trustType string
}

// Microsoft domain
private microsoft.domain @defaults("id") {
  // Domain ID
  id string
  // Domain authentication type
  authenticationType string
  // Domain availability status
  availabilityStatus string
  // Whether the domain is admin managed
  isAdminManaged bool
  // Whether the domain is the default domain
  isDefault bool
  // Whether the domain is the initial domain
  isInitial bool
  // Whether the domain is a root domain
  isRoot bool
  // Whether the domain is verified
  isVerified bool
  // Domain password notification window (days)
  passwordNotificationWindowInDays int
  // Domain password validity period (days)
  passwordValidityPeriodInDays int
  // List of supported services
  supportedServices []string
  // List of service configuration records
  serviceConfigurationRecords() []microsoft.domaindnsrecord
}

// Microsoft domain DNS record
private microsoft.domaindnsrecord @defaults("id label") {
  // Domain record ID
  id string
  // Whether the domain record is optional
  isOptional bool
  // Domain record label
  label string
  // Domain record type
  recordType string
  // Domain record supported service
  supportedService string
  // Domain record TTL
  ttl int
  // Deprecated; kept for backwards compatibility
  properties dict
}

// Microsoft Entra ID application registration
microsoft.application @defaults("id displayName hasExpiredCredentials") {
  init(name string, id string)
  // Object ID
  id string
  // Application (client) ID
  appId string
  // Application display name
  name string
  // Deprecated: Use `name` instead
  displayName string
  // Description
  description string
  // Notes
  notes string
  // Tags
  tags []string
  // Application template ID
  applicationTemplateId string
  // Microsoft disabled status
  disabledByMicrosoftStatus string
  // Group membership claims
  groupMembershipClaims string
  // Application creation date
  createdAt time
  // Deprecated: Use `createdAt` instead
  createdDateTime time
  // Application identifier URIs
  identifierUris []string
  // Application publisher domain
  publisherDomain string
  // Application sign-in audience
  signInAudience string
  // Basic profile information
  info dict
  // Settings for an application that implements a web API
  api dict
  // Settings for a web application
  web dict
  // Settings for a single-page application
  spa dict
  // Client secrets
  secrets []microsoft.passwordCredential
  // Certificates
  certificates []microsoft.keyCredential
  // Whether the credentials have expired
  hasExpiredCredentials() bool
  // Application owner
  owners() []microsoft.user
  // Managed application in local directory
  servicePrincipal() microsoft.serviceprincipal
  // Whether the application supports device-only authentication
  isDeviceOnlyAuthSupported bool
  // Specifies the fallback application type as public client
  isFallbackPublicClient bool
  // Whether the application supports native authentication
  nativeAuthenticationApisEnabled string
  // Service management reference
  serviceManagementReference string
  // Token encryption key ID
  tokenEncryptionKeyId string
  // SAML metadata URL
  samlMetadataUrl string
  // Default redirect URI
  defaultRedirectUri string
  // Certification metadata
  certification dict
  // Optional claims
  optionalClaims dict
  // Service principal configuration
  servicePrincipalLockConfiguration dict
  // Signature verification
  requestSignatureVerification dict
  // Parental control settings
  parentalControlSettings dict
  // Public client configuration
  publicClient dict
  // Application roles
  appRoles []microsoft.application.role
}

// Microsoft Entra ID app roles are custom roles to assign permissions to users or apps
private microsoft.application.role @defaults("name value isEnabled"){
  // App role ID
  id string
  // Display name
  name string
  // Description
  description string
  // Value
  value string
  // Allowed member types
  allowedMemberTypes []string
  // App state
  isEnabled bool
}

// Microsoft Entra AD Application certificate
private microsoft.keyCredential @defaults("thumbprint description expires keyId") {
  // Certificate ID
  keyId string
  // Description
  description string
  // Certificate thumbprint
  thumbprint string
  // Certificate type
  type string
  // Certificate usage
  usage string
  // Certificate expiration date
  expires time
  // Whether the secret has expired
  expired bool
}

// Microsoft Entra AD Application secrets
private microsoft.passwordCredential @defaults("description expires keyId") {
  // Secret ID
  keyId string
  // Description
  description string
  // Secret hint
  hint string
  // Secret expiration date
  expires time
  // Whether the secret has expired
  expired bool
}

// Microsoft service principal (Enterprise application)
microsoft.serviceprincipal @defaults("name") {
  // Service principal Object ID
  id string
  // Service principal type
  type string
  // Service principal name
  name string
  // Application ID
  appId string
  // Application owner ID
  appOwnerOrganizationId string
  // Application description
  description string
  // Service principal tags
  tags []string
  // Whether users can sign into the service principal (application)
  enabled bool
  // Service principal homepage URL
  homepageUrl string
  // Service principal terms of service URL
  termsOfServiceUrl string
  // Service principal reply URLs
  replyUrls []string
  // Whether users or other apps must be assigned to this service principal before using it
  assignmentRequired bool
  // Whether the service principal is visible to users
  visibleToUsers bool
  // Service principal notes
  notes string
  // List of users and groups assigned to this service principal
  assignments []microsoft.serviceprincipal.assignment
  // Application template ID
  applicationTemplateId string
  // Application publisher
  verifiedPublisher dict
  // Login URL
  loginUrl string
  // Logout URL
  logoutUrl string
  // Service principal names
  servicePrincipalNames []string
  // Sign in audience
  signInAudience string
  // Preferred single sign-on mode
  preferredSingleSignOnMode string
  // Notification email addresses
  notificationEmailAddresses []string
  // App role assignment required
  appRoleAssignmentRequired bool
  // Deprecated: use `enabled` instead
  accountEnabled bool
  // Whether it is a first-party Microsoft application
  isFirstParty() bool
  // Application roles
  appRoles []microsoft.application.role
  // Permissions
  permissions() []microsoft.application.permission
}

// Microsoft Service Principal Assignment
private microsoft.serviceprincipal.assignment @defaults("id") {
  // Service Principal Assignment ID
  id string
  // Service Principal Assignment name
  displayName string
  // Service Principal Assignment type
  type string
}

// Microsoft Service Principal Permission
private microsoft.application.permission @defaults("appName name type status") {
  // ID of the API
  appId string
  // Name of the API
  appName string
  // Permission ID
  id  string
  // Permission name
  name string
  // Permission description
  description string
  // Type eg. `application` or `delegated`
  type string
  // Status
  status string
}

// Microsoft Security
microsoft.security {
  // List of security scores
  secureScores() []microsoft.security.securityscore
  // Latest security score
  latestSecureScores() microsoft.security.securityscore
  // List Microsoft Entra users who are at risk
  riskyUsers() []microsoft.security.riskyUser
}

// Microsoft Secure Score
private microsoft.security.securityscore @defaults("id azureTenantId") {
  // Secure Score ID
  id string
  // Secure Score active user count
  activeUserCount int
  // Secure Score average comparative score
  averageComparativeScores []dict
  // Secure Score tenant ID
  azureTenantId string
  // Secure Score control scores
  controlScores []dict
  // Secure Score creation time
  createdDateTime time
  // Secure Score current score
  currentScore float
  // Secure Score enabled services
  enabledServices []string
  // Secure Score licensed user count
  licensedUserCount int
  // Secure Score max score
  maxScore float
  // Secure Score vendor information
  vendorInformation dict
}

// Microsoft Entra users who are at risk
microsoft.security.riskyUser @defaults("principalName riskLevel riskState lastUpdatedAt"){
  // Risky user ID
  id string
  // User name
  name string
  // User principal
  principalName string
  // Entra User
  user() microsoft.user
  // Risk detail
  riskDetail string
  // Risk level
  riskLevel string
  // Risk state
  riskState string
  // Risk last updated
  lastUpdatedAt time
}

// Microsoft policies
microsoft.policies {
  // Authorization policy
  authorizationPolicy() dict
  // Identity security default enforcement policy
  identitySecurityDefaultsEnforcementPolicy() dict
  // Admin consent request policy
  adminConsentRequestPolicy() microsoft.adminConsentRequestPolicy
  // Permission grant policies
  permissionGrantPolicies() []dict
  // Consent policy settings
  consentPolicySettings() dict
  // Authentication methods policy
  authenticationMethodsPolicy() microsoft.policies.authenticationMethodsPolicy
}

private microsoft.adminConsentRequestPolicy @defaults("isEnabled notifyReviewers") {
  // Specifies whether the admin consent request feature is enabled or disabled
  isEnabled bool
  // Specifies whether reviewers will receive notifications
  notifyReviewers bool
  // Specifies whether reviewers will receive reminder emails
  remindersEnabled bool
  // Specifies the duration the request is active before it automatically expires if no decision is applied
  requestDurationInDays int
  // The list of reviewers for the admin consent
  reviewers []microsoft.graph.accessReviewReviewerScope
  // Specifies the version of this policy
  version int
}

private microsoft.graph.accessReviewReviewerScope @defaults("query queryType") {
  // The query specifying who will be the reviewer
  query string
  // In the scenario where reviewers need to be specified dynamically, this property is used to indicate the relative source of the query
  queryRoot string
  // The type of query
  queryType string
}

// The tenant-wide policy that controls which authentication methods are allowed
private microsoft.policies.authenticationMethodsPolicy @defaults("displayName") {
  // Policy ID
  id string
  // Policy display name
  displayName string
  // Policy description
  description string
  // The date and time the policy was last modified
  lastModifiedDateTime time
  // The version of the policy
  policyVersion string
  // Configurations for specific authentication methods
  authenticationMethodConfigurations []microsoft.policies.authenticationMethodConfiguration
}

// Configuration for a specific authentication method
private microsoft.policies.authenticationMethodConfiguration @defaults("state") {
  // The policy name
  id string
  // The state of the policy. Possible values are: enabled, disabled.
  state string
  // Groups of users that are excluded from a policy.
  excludeTargets []dict
}

// List of Microsoft Entra role definitions with optional filters
microsoft.roles {
  []microsoft.rolemanagement.roledefinition

  init(filter? string, search? string)
  // Filter roles by property values
  filter string
  // Search roles by search phrases
  search string
}

// Deprecated: use `microsoft.roles` instead
microsoft.rolemanagement {
  // Deprecated: use `microsoft.roles` instead
  roleDefinitions() microsoft.roles
}

// Microsoft role definition
private microsoft.rolemanagement.roledefinition @defaults("id displayName") {
  // Role definition ID
  id string
  // Role definition description
  description string
  // Role definition display name
  displayName string
  // Whether the role is built in
  isBuiltIn bool
  // Whether the role is enabled
  isEnabled bool
  // Role definition permissions
  rolePermissions []dict
  // Role definition template ID
  templateId string
  // Role definition version
  version string
  // List of role definition assignments
  assignments() []microsoft.rolemanagement.roleassignment
}

// Microsoft role assignment
private microsoft.rolemanagement.roleassignment @defaults("id principalId") {
  // Role assignment ID
  id string
  // Role definition ID
  roleDefinitionId string
  // Service principal ID
  principalId string
  // Service principal
  principal dict
}

// Microsoft device management
microsoft.devicemanagement {
  managedDevices() []microsoft.devicemanagement.manageddevice
  // List of device configurations
  deviceConfigurations() []microsoft.devicemanagement.deviceconfiguration
  // List of device compliance policies
  deviceCompliancePolicies() []microsoft.devicemanagement.devicecompliancepolicy
  // Device Enrollment Configuration
  deviceEnrollmentConfigurations() []microsoft.devicemanagement.deviceEnrollmentConfiguration
  // Device management settings
  settings() microsoft.devicemanagement.settings
}

// Microsoft Device Enrollment Configuration
private microsoft.devicemanagement.deviceEnrollmentConfiguration @defaults("id displayName") {
  // Unique identifier for the account
  id string
  // The display name of the device enrollment configuration
  displayName string
  // The description of the device enrollment configuration
  description string
  // Priority is used when a user exists in multiple groups that are assigned enrollment configuration
  priority int
  // Created date time in UTC of the device enrollment configuration
  createdDateTime time
  // Last modified date time in UTC of the device enrollment configuration
  lastModifiedDateTime time
  // The version of the device enrollment configuration
  version int
}

// Microsoft managed device
private microsoft.devicemanagement.manageddevice {
  // Managed device ID
  id string
  // Unique identifier for the user associated with the device
  userId string
  // Name of the device
  name string
  // Operating system of the device
  operatingSystem string
  // Whether the device is jail broken or rooted
  jailBroken string
  // Operating system version of the device
  osVersion string
  // Whether the device is Exchange ActiveSync activated
  easActivated bool
  // Exchange ActiveSync Id of the device
  easDeviceId string
  // Whether the device is Entra ID (previously Azure AD) registered
  azureADRegistered bool
  // Email(s) for the user associated with the device
  emailAddress string
  // The unique identifier for the Entra ID (previously Azure AD) device
  azureActiveDirectoryDeviceId string
  // Device category display name
  deviceCategoryDisplayName string
  // Device supervised status
  isSupervised bool
  // Device encryption status
  isEncrypted bool
  // Device user principal name
  userPrincipalName string
  // Device model
  model string
  // Device manufacturer
  manufacturer string
  // International Mobile Equipment Identity (IMEI)
  imei string
  // Serial number of the device
  serialNumber string
  // Android security patch level
  androidSecurityPatchLevel string
  // User display name
  userDisplayName string
  // Wi-Fi MAC address
  wiFiMacAddress string
  // Mobile Equipment Identifier (MEID)
  meid string
  // Integrated Circuit Card Identifier (ICCID)
  iccid string
  // Unique Device Identifier (UDID) for iOS and macOS devices
  udid string
  // Notes on the device created by IT
  notes string
  // Ethernet MAC address of the device
  ethernetMacAddress string
  // Name of the enrollment profile assigned to the device
  enrollmentProfileName string
  // Protection state of the device
  windowsProtectionState dict
}

// Microsoft device configuration
private microsoft.devicemanagement.deviceconfiguration @defaults("id displayName") {
  // Device configuration ID
  id string
  // Device configuration last modify date
  lastModifiedDateTime time
  // Device configuration creation date
  createdDateTime time
  // Device configuration description
  description string
  // Device configuration display name
  displayName string
  // Device configuration version
  version int
  // Deprecated; kept for backwards compatibility
  properties dict
}

// Microsoft device compliance policy
private microsoft.devicemanagement.devicecompliancepolicy @defaults("id displayName") {
  // Device compliance policy ID
  id string
  // Device compliance policy creation date
  createdDateTime time
  // Device compliance policy description
  description string
  // Device compliance policy display name
  displayName string
  // Device compliance policy last modified date
  lastModifiedDateTime time
  // Device compliance policy version
  version int
  // Device compliance policy assignments
  assignments []dict
  // Deprecated; kept for backwards compatibility
  properties dict
}

// Microsoft device management settings
private microsoft.devicemanagement.settings {
  // Device should be noncompliant when there is no compliance policy targeted when this is true
  secureByDefault bool
  // Is feature enabled or not for scheduled action for rule
  isScheduledActionEnabled bool
  // The number of days a device is allowed to go without checking in to remain compliant
  deviceComplianceCheckinThresholdDays int
}

// Microsoft 365 Exchange Online
ms365.exchangeonline {
  // List of malware filter policies
  malwareFilterPolicy() []dict
  // List of hosted outbound spam filter policies
  hostedOutboundSpamFilterPolicy() []dict
  // Transport rules
  transportRule() []dict
  // List of remote domains
  remoteDomain() []dict
  // List of safe links policies
  safeLinksPolicy() []dict
  // List of safe attachment policies
  safeAttachmentPolicy() []dict
  // Organization configurations
  organizationConfig() dict
  // List of authentication policies
  authenticationPolicy() []dict
  // List of anti-phishing policies
  antiPhishPolicy() []dict
  // List of dkim signing configurations
  dkimSigningConfig() []dict
  // List of OWA mailbox policies
  owaMailboxPolicy() []dict
  // Admin audit log configuration
  adminAuditLogConfig() dict
  // List of phishing filter policies
  phishFilterPolicy() []dict
  // List of mailboxes
  mailbox() []dict
  // List of APT policies for Office 365
  atpPolicyForO365() []dict
  // List of sharing policies
  sharingPolicy() []dict
  // List of role assignment policies
  roleAssignmentPolicy() []dict
  // List of external sender configurations
  externalInOutlook() []ms365.exchangeonline.externalSender
  // List of shared mailboxes
  sharedMailboxes() []ms365.exchangeonline.exoMailbox
  // List of Teams protection policies
  teamsProtectionPolicies() []ms365.exchangeonline.teamsProtectionPolicy
  // List of report submission policies
  reportSubmissionPolicies() []ms365.exchangeonline.reportSubmissionPolicy
  // List of mailboxes with audit settings
  mailboxesWithAudit() []ms365.exchangeonline.mailbox
  // Transport configuration settings
  transportConfig() dict
  // Security and Compliance policies
  securityAndCompliance() ms365.exchangeonline.securityAndCompliance
}

// Microsoft 365 Exchange Online Security and Compliance
private ms365.exchangeonline.securityAndCompliance {
  // List of Data Loss Prevention (DLP) compliance policies
  dlpCompliancePolicies() []dict
}

// Teams Protection Policy configuration
private ms365.exchangeonline.teamsProtectionPolicy {
  // Whether Zero-hour Auto Purge (ZAP) is enabled
  zapEnabled bool
  // Whether the policy configuration is considered valid
  isValid bool
}

// Report Submission Policy configuration
private ms365.exchangeonline.reportSubmissionPolicy {
  // Whether to report junk to a customized address
  reportJunkToCustomizedAddress bool
  // Whether to report not junk to a customized address
  reportNotJunkToCustomizedAddress bool
  // Whether to report phish to a customized address
  reportPhishToCustomizedAddress bool
  // Addresses to report junk
  reportJunkAddresses []string
  // Addresses to report not junk
  reportNotJunkAddresses []string
  // Addresses to report phish
  reportPhishAddresses []string
  // Whether chat message reporting is enabled
  reportChatMessageEnabled bool
  // Whether to report chat messages to a customized address
  reportChatMessageToCustomizedAddressEnabled bool
}

// Microsoft 365 Exchange Online External Sender
private ms365.exchangeonline.externalSender {
  // The identity of the external sender
  identity string
  // The list of specified senders that do not receive the external icon in the area of subject line
  allowList []string
  // Whether the feature is enabled
  enabled bool
}

// Microsoft 365 Exchange Online Mailbox
private ms365.exchangeonline.exoMailbox {
  // The identity of the mailbox
  identity string
  // The user linked to this mailbox
  user() microsoft.user
  // The identity of the external object linked to this mailbox
  externalDirectoryObjectId string
}

// Microsoft 365 Exchange Online Mailbox with Audit Settings
private ms365.exchangeonline.mailbox @defaults("identity displayName auditEnabled") {
  // Mailbox identity
  identity string
  // Display name
  displayName string
  // Primary SMTP address
  primarySmtpAddress string
  // Recipient type details
  recipientTypeDetails string
  // Whether audit logging is enabled
  auditEnabled bool
  // Admin audit actions
  auditAdmin []string
  // Delegate audit actions
  auditDelegate []string
  // Owner audit actions
  auditOwner []string
  // Audit log age limit
  auditLogAgeLimit string
}

// Microsoft 365 SharePoint Online
ms365.sharepointonline {
  // SharePoint Online tenant
  spoTenant() dict
  // SharePoint Online tenant sync client restriction
  spoTenantSyncClientRestriction() dict
  // SharePoint Online tenant sites
  spoSites() []ms365.sharepointonline.site
  // The default link permission for the tenant
  defaultLinkPermission() string
}

// Microsoft 365 SharePoint Site
private ms365.sharepointonline.site {
  // The site URL
  url string
  // Whether custom script execution on a particular site allowed
  denyAddAndCustomizePages bool
}

// Microsoft 365 Teams
ms365.teams {
  // Teams client configuration
  csTeamsClientConfiguration() dict
  // Teams tenant federated configuration
  csTenantFederationConfiguration() ms365.teams.tenantFederationConfig
  // Teams meeting policy configuration
  csTeamsMeetingPolicy() ms365.teams.teamsMeetingPolicyConfig
  // Teams message policy configuration
  csTeamsMessagingPolicy() ms365.teams.teamsMessagingPolicyConfig
}

// Microsoft 365 Teams tenant federation configuration
private ms365.teams.tenantFederationConfig {
  // ID of the collection of tenant federation configuration settings
  identity string
  // Blocked domains
  blockedDomains dict
  // Allowed domains
  allowedDomains []string
  // Whether federated users are allowed
  allowFederatedUsers bool
  // Whether public users are allowed
  allowPublicUsers bool
  // Whether to allow external Teams consumers
  allowTeamsConsumer bool
  // Whether to allow inbound communication with external Teams consumers
  allowTeamsConsumerInbound bool
  // Whether to treat discovered partners as unverified
  treatDiscoveredPartnersAsUnverified bool
  // Whether shared SIP address space is enabled
  sharedSipAddressSpace bool
  // Whether to restrict Teams consumer to external user profiles
  restrictTeamsConsumerToExternalUserProfiles bool
}

// Microsoft 365 Teams meeting policy configuration
private ms365.teams.teamsMeetingPolicyConfig {
  // Whether anonymous users are allowed to join
  allowAnonymousUsersToJoinMeeting bool
  // Whether anonymous users are allowed to start the meeting
  allowAnonymousUsersToStartMeeting bool
  // Whether external meeting chat is allowed
  allowExternalNonTrustedMeetingChat bool
  // Who can bypass the lobby
  autoAdmittedUsers string
  // Whether public switched telephone network (PSTN) users can bypass the lobby
  allowPSTNUsersToBypassLobby bool
  // Whether meeting chat is enabled
  meetingChatEnabledType string
  // Designated presenter role mode
  designatedPresenterRoleMode string
  // Whether external participants can give or request control of the meeting
  allowExternalParticipantGiveRequestControl bool
  // Whether users can report security concerns
  allowSecurityEndUserReporting bool
  // Whether cloud recording is allowed
  allowCloudRecordingForCalls bool
}

// Teams meeting policy configuration
private ms365.teams.teamsMessagingPolicyConfig {
  // Whether users can report security concerns
  allowSecurityEndUserReporting bool
}
