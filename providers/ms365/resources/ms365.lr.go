// Copyright (c) Mondoo, Inc.
// SPDX-License-Identifier: BUSL-1.1

// Code generated by resources. DO NOT EDIT.

package resources

import (
	"errors"
	"time"

	"go.mondoo.com/cnquery/v11/llx"
	"go.mondoo.com/cnquery/v11/providers-sdk/v1/plugin"
	"go.mondoo.com/cnquery/v11/types"
)

var resourceFactories map[string]plugin.ResourceFactory

func init() {
	resourceFactories = map[string]plugin.ResourceFactory {
		"microsoft": {
			// to override args, implement: initMicrosoft(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoft,
		},
		"microsoft.groups": {
			Init: initMicrosoftGroups,
			Create: createMicrosoftGroups,
		},
		"microsoft.applications": {
			// to override args, implement: initMicrosoftApplications(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftApplications,
		},
		"microsoft.tenant": {
			Init: initMicrosoftTenant,
			Create: createMicrosoftTenant,
		},
		"microsoft.tenant.settings": {
			// to override args, implement: initMicrosoftTenantSettings(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftTenantSettings,
		},
		"microsoft.tenant.formsSettings": {
			// to override args, implement: initMicrosoftTenantFormsSettings(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftTenantFormsSettings,
		},
		"microsoft.users": {
			Init: initMicrosoftUsers,
			Create: createMicrosoftUsers,
		},
		"microsoft.identityAndAccess": {
			Init: initMicrosoftIdentityAndAccess,
			Create: createMicrosoftIdentityAndAccess,
		},
		"microsoft.identityAndAccess.roleEligibilityScheduleInstance": {
			// to override args, implement: initMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance,
		},
		"microsoft.identityAndAccess.policy": {
			// to override args, implement: initMicrosoftIdentityAndAccessPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftIdentityAndAccessPolicy,
		},
		"microsoft.identityAndAccess.policy.rule": {
			// to override args, implement: initMicrosoftIdentityAndAccessPolicyRule(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftIdentityAndAccessPolicyRule,
		},
		"microsoft.identityAndAccess.policy.rule.target": {
			// to override args, implement: initMicrosoftIdentityAndAccessPolicyRuleTarget(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftIdentityAndAccessPolicyRuleTarget,
		},
		"microsoft.user.assignedLicense": {
			// to override args, implement: initMicrosoftUserAssignedLicense(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserAssignedLicense,
		},
		"microsoft.user.licenseDetail": {
			// to override args, implement: initMicrosoftUserLicenseDetail(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserLicenseDetail,
		},
		"microsoft.user.licenseDetail.servicePlanInfo": {
			// to override args, implement: initMicrosoftUserLicenseDetailServicePlanInfo(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserLicenseDetailServicePlanInfo,
		},
		"microsoft.conditionalAccess": {
			// to override args, implement: initMicrosoftConditionalAccess(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccess,
		},
		"microsoft.conditionalAccess.authenticationMethodsPolicy": {
			// to override args, implement: initMicrosoftConditionalAccessAuthenticationMethodsPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessAuthenticationMethodsPolicy,
		},
		"microsoft.conditionalAccess.authenticationMethodConfiguration": {
			// to override args, implement: initMicrosoftConditionalAccessAuthenticationMethodConfiguration(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessAuthenticationMethodConfiguration,
		},
		"microsoft.conditionalAccess.namedLocations": {
			// to override args, implement: initMicrosoftConditionalAccessNamedLocations(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessNamedLocations,
		},
		"microsoft.conditionalAccess.policy": {
			// to override args, implement: initMicrosoftConditionalAccessPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicy,
		},
		"microsoft.conditionalAccess.policy.conditions": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyConditions(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyConditions,
		},
		"microsoft.conditionalAccess.policy.conditions.authenticationFlows": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows,
		},
		"microsoft.conditionalAccess.policy.grantControls.authenticationStrength": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength,
		},
		"microsoft.conditionalAccess.policy.sessionControls.signInFrequency": {
			// to override args, implement: initMicrosoftConditionalAccessPolicySessionControlsSignInFrequency(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicySessionControlsSignInFrequency,
		},
		"microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity": {
			// to override args, implement: initMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity,
		},
		"microsoft.conditionalAccess.policy.sessionControls.persistentBrowser": {
			// to override args, implement: initMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser,
		},
		"microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions": {
			// to override args, implement: initMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions,
		},
		"microsoft.conditionalAccess.policy.conditions.clientApplications": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyConditionsClientApplications(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyConditionsClientApplications,
		},
		"microsoft.conditionalAccess.policy.conditions.platforms": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyConditionsPlatforms(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyConditionsPlatforms,
		},
		"microsoft.conditionalAccess.policy.conditions.applications": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyConditionsApplications(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyConditionsApplications,
		},
		"microsoft.conditionalAccess.policy.conditions.users": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyConditionsUsers(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyConditionsUsers,
		},
		"microsoft.conditionalAccess.policy.conditions.locations": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyConditionsLocations(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyConditionsLocations,
		},
		"microsoft.conditionalAccess.policy.grantControls": {
			// to override args, implement: initMicrosoftConditionalAccessPolicyGrantControls(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicyGrantControls,
		},
		"microsoft.conditionalAccess.policy.sessionControls": {
			// to override args, implement: initMicrosoftConditionalAccessPolicySessionControls(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessPolicySessionControls,
		},
		"microsoft.conditionalAccess.ipNamedLocation": {
			// to override args, implement: initMicrosoftConditionalAccessIpNamedLocation(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessIpNamedLocation,
		},
		"microsoft.conditionalAccess.countryNamedLocation": {
			// to override args, implement: initMicrosoftConditionalAccessCountryNamedLocation(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftConditionalAccessCountryNamedLocation,
		},
		"microsoft.user": {
			Init: initMicrosoftUser,
			Create: createMicrosoftUser,
		},
		"microsoft.user.authenticationRequirements": {
			// to override args, implement: initMicrosoftUserAuthenticationRequirements(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserAuthenticationRequirements,
		},
		"microsoft.user.auditlog": {
			// to override args, implement: initMicrosoftUserAuditlog(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserAuditlog,
		},
		"microsoft.user.identity": {
			// to override args, implement: initMicrosoftUserIdentity(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserIdentity,
		},
		"microsoft.user.signin": {
			// to override args, implement: initMicrosoftUserSignin(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserSignin,
		},
		"microsoft.user.authenticationMethods": {
			// to override args, implement: initMicrosoftUserAuthenticationMethods(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserAuthenticationMethods,
		},
		"microsoft.user.authenticationMethods.userRegistrationDetails": {
			// to override args, implement: initMicrosoftUserAuthenticationMethodsUserRegistrationDetails(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftUserAuthenticationMethodsUserRegistrationDetails,
		},
		"microsoft.group": {
			// to override args, implement: initMicrosoftGroup(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftGroup,
		},
		"microsoft.devices": {
			Init: initMicrosoftDevices,
			Create: createMicrosoftDevices,
		},
		"microsoft.device": {
			Init: initMicrosoftDevice,
			Create: createMicrosoftDevice,
		},
		"microsoft.domain": {
			// to override args, implement: initMicrosoftDomain(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftDomain,
		},
		"microsoft.domaindnsrecord": {
			// to override args, implement: initMicrosoftDomaindnsrecord(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftDomaindnsrecord,
		},
		"microsoft.application": {
			Init: initMicrosoftApplication,
			Create: createMicrosoftApplication,
		},
		"microsoft.application.role": {
			// to override args, implement: initMicrosoftApplicationRole(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftApplicationRole,
		},
		"microsoft.keyCredential": {
			// to override args, implement: initMicrosoftKeyCredential(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftKeyCredential,
		},
		"microsoft.passwordCredential": {
			// to override args, implement: initMicrosoftPasswordCredential(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftPasswordCredential,
		},
		"microsoft.serviceprincipal": {
			Init: initMicrosoftServiceprincipal,
			Create: createMicrosoftServiceprincipal,
		},
		"microsoft.serviceprincipal.assignment": {
			// to override args, implement: initMicrosoftServiceprincipalAssignment(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftServiceprincipalAssignment,
		},
		"microsoft.application.permission": {
			// to override args, implement: initMicrosoftApplicationPermission(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftApplicationPermission,
		},
		"microsoft.security": {
			// to override args, implement: initMicrosoftSecurity(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftSecurity,
		},
		"microsoft.security.securityscore": {
			// to override args, implement: initMicrosoftSecuritySecurityscore(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftSecuritySecurityscore,
		},
		"microsoft.security.riskyUser": {
			// to override args, implement: initMicrosoftSecurityRiskyUser(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftSecurityRiskyUser,
		},
		"microsoft.policies": {
			// to override args, implement: initMicrosoftPolicies(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftPolicies,
		},
		"microsoft.adminConsentRequestPolicy": {
			// to override args, implement: initMicrosoftAdminConsentRequestPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftAdminConsentRequestPolicy,
		},
		"microsoft.graph.accessReviewReviewerScope": {
			// to override args, implement: initMicrosoftGraphAccessReviewReviewerScope(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftGraphAccessReviewReviewerScope,
		},
		"microsoft.policies.authenticationMethodsPolicy": {
			// to override args, implement: initMicrosoftPoliciesAuthenticationMethodsPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftPoliciesAuthenticationMethodsPolicy,
		},
		"microsoft.policies.authenticationMethodConfiguration": {
			// to override args, implement: initMicrosoftPoliciesAuthenticationMethodConfiguration(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftPoliciesAuthenticationMethodConfiguration,
		},
		"microsoft.roles": {
			Init: initMicrosoftRoles,
			Create: createMicrosoftRoles,
		},
		"microsoft.rolemanagement": {
			// to override args, implement: initMicrosoftRolemanagement(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftRolemanagement,
		},
		"microsoft.rolemanagement.roledefinition": {
			// to override args, implement: initMicrosoftRolemanagementRoledefinition(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftRolemanagementRoledefinition,
		},
		"microsoft.rolemanagement.roleassignment": {
			// to override args, implement: initMicrosoftRolemanagementRoleassignment(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftRolemanagementRoleassignment,
		},
		"microsoft.devicemanagement": {
			// to override args, implement: initMicrosoftDevicemanagement(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftDevicemanagement,
		},
		"microsoft.devicemanagement.deviceEnrollmentConfiguration": {
			// to override args, implement: initMicrosoftDevicemanagementDeviceEnrollmentConfiguration(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftDevicemanagementDeviceEnrollmentConfiguration,
		},
		"microsoft.devicemanagement.manageddevice": {
			// to override args, implement: initMicrosoftDevicemanagementManageddevice(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftDevicemanagementManageddevice,
		},
		"microsoft.devicemanagement.deviceconfiguration": {
			// to override args, implement: initMicrosoftDevicemanagementDeviceconfiguration(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftDevicemanagementDeviceconfiguration,
		},
		"microsoft.devicemanagement.devicecompliancepolicy": {
			// to override args, implement: initMicrosoftDevicemanagementDevicecompliancepolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftDevicemanagementDevicecompliancepolicy,
		},
		"microsoft.devicemanagement.settings": {
			// to override args, implement: initMicrosoftDevicemanagementSettings(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMicrosoftDevicemanagementSettings,
		},
		"ms365.exchangeonline": {
			// to override args, implement: initMs365Exchangeonline(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365Exchangeonline,
		},
		"ms365.exchangeonline.securityAndCompliance": {
			// to override args, implement: initMs365ExchangeonlineSecurityAndCompliance(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365ExchangeonlineSecurityAndCompliance,
		},
		"ms365.exchangeonline.teamsProtectionPolicy": {
			// to override args, implement: initMs365ExchangeonlineTeamsProtectionPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365ExchangeonlineTeamsProtectionPolicy,
		},
		"ms365.exchangeonline.reportSubmissionPolicy": {
			// to override args, implement: initMs365ExchangeonlineReportSubmissionPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365ExchangeonlineReportSubmissionPolicy,
		},
		"ms365.exchangeonline.externalSender": {
			// to override args, implement: initMs365ExchangeonlineExternalSender(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365ExchangeonlineExternalSender,
		},
		"ms365.exchangeonline.exoMailbox": {
			// to override args, implement: initMs365ExchangeonlineExoMailbox(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365ExchangeonlineExoMailbox,
		},
		"ms365.exchangeonline.mailbox": {
			// to override args, implement: initMs365ExchangeonlineMailbox(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365ExchangeonlineMailbox,
		},
		"ms365.sharepointonline": {
			// to override args, implement: initMs365Sharepointonline(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365Sharepointonline,
		},
		"ms365.sharepointonline.site": {
			// to override args, implement: initMs365SharepointonlineSite(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365SharepointonlineSite,
		},
		"ms365.teams": {
			// to override args, implement: initMs365Teams(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365Teams,
		},
		"ms365.teams.tenantFederationConfig": {
			// to override args, implement: initMs365TeamsTenantFederationConfig(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365TeamsTenantFederationConfig,
		},
		"ms365.teams.teamsMeetingPolicyConfig": {
			// to override args, implement: initMs365TeamsTeamsMeetingPolicyConfig(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365TeamsTeamsMeetingPolicyConfig,
		},
		"ms365.teams.teamsMessagingPolicyConfig": {
			// to override args, implement: initMs365TeamsTeamsMessagingPolicyConfig(runtime *plugin.Runtime, args map[string]*llx.RawData) (map[string]*llx.RawData, plugin.Resource, error)
			Create: createMs365TeamsTeamsMessagingPolicyConfig,
		},
	}
}

// NewResource is used by the runtime of this plugin to create new resources.
// Its arguments may be provided by users. This function is generally not
// used by initializing resources from recordings or from lists.
func NewResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	if f.Init != nil {
		cargs, res, err := f.Init(runtime, args)
		if err != nil {
			return res, err
		}

		if res != nil {
			id := name+"\x00"+res.MqlID()
			if x, ok := runtime.Resources.Get(id); ok {
				return x, nil
			}
			runtime.Resources.Set(id, res)
			return res, nil
		}

		args = cargs
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

// CreateResource is used by the runtime of this plugin to create resources.
// Its arguments must be complete and pre-processed. This method is used
// for initializing resources from recordings or from lists.
func CreateResource(runtime *plugin.Runtime, name string, args map[string]*llx.RawData) (plugin.Resource, error) {
	f, ok := resourceFactories[name]
	if !ok {
		return nil, errors.New("cannot find resource " + name + " in this provider")
	}

	res, err := f.Create(runtime, args)
	if err != nil {
		return nil, err
	}

	id := name+"\x00"+res.MqlID()
	if x, ok := runtime.Resources.Get(id); ok {
		return x, nil
	}

	runtime.Resources.Set(id, res)
	return res, nil
}

var getDataFields = map[string]func(r plugin.Resource) *plugin.DataRes{
	"microsoft.organizations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetOrganizations()).ToDataRes(types.Array(types.Resource("microsoft.tenant")))
	},
	"microsoft.users": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetUsers()).ToDataRes(types.Resource("microsoft.users"))
	},
	"microsoft.groups": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetGroups()).ToDataRes(types.Resource("microsoft.groups"))
	},
	"microsoft.domains": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetDomains()).ToDataRes(types.Array(types.Resource("microsoft.domain")))
	},
	"microsoft.applications": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetApplications()).ToDataRes(types.Resource("microsoft.applications"))
	},
	"microsoft.serviceprincipals": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetServiceprincipals()).ToDataRes(types.Array(types.Resource("microsoft.serviceprincipal")))
	},
	"microsoft.enterpriseApplications": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetEnterpriseApplications()).ToDataRes(types.Array(types.Resource("microsoft.serviceprincipal")))
	},
	"microsoft.roles": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetRoles()).ToDataRes(types.Resource("microsoft.roles"))
	},
	"microsoft.settings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetSettings()).ToDataRes(types.Dict)
	},
	"microsoft.tenantDomainName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetTenantDomainName()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoft).GetIdentityAndAccess()).ToDataRes(types.Resource("microsoft.identityAndAccess"))
	},
	"microsoft.groups.length": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroups).GetLength()).ToDataRes(types.Int)
	},
	"microsoft.groups.list": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroups).GetList()).ToDataRes(types.Array(types.Resource("microsoft.group")))
	},
	"microsoft.applications.length": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplications).GetLength()).ToDataRes(types.Int)
	},
	"microsoft.applications.list": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplications).GetList()).ToDataRes(types.Array(types.Resource("microsoft.application")))
	},
	"microsoft.tenant.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetId()).ToDataRes(types.String)
	},
	"microsoft.tenant.assignedPlans": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetAssignedPlans()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.tenant.provisionedPlans": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetProvisionedPlans()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.tenant.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.tenant.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.tenant.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetName()).ToDataRes(types.String)
	},
	"microsoft.tenant.verifiedDomains": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetVerifiedDomains()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.tenant.onPremisesSyncEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetOnPremisesSyncEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetCreatedAt()).ToDataRes(types.Time)
	},
	"microsoft.tenant.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetType()).ToDataRes(types.String)
	},
	"microsoft.tenant.subscriptions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetSubscriptions()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.tenant.settings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetSettings()).ToDataRes(types.Resource("microsoft.tenant.settings"))
	},
	"microsoft.tenant.formsSettings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetFormsSettings()).ToDataRes(types.Resource("microsoft.tenant.formsSettings"))
	},
	"microsoft.tenant.privacyProfile": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetPrivacyProfile()).ToDataRes(types.Dict)
	},
	"microsoft.tenant.technicalNotificationMails": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetTechnicalNotificationMails()).ToDataRes(types.Array(types.String))
	},
	"microsoft.tenant.preferredLanguage": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenant).GetPreferredLanguage()).ToDataRes(types.String)
	},
	"microsoft.tenant.settings.isAppAndServicesTrialEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantSettings).GetIsAppAndServicesTrialEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.settings.isOfficeStoreEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantSettings).GetIsOfficeStoreEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.formsSettings.isExternalSendFormEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantFormsSettings).GetIsExternalSendFormEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.formsSettings.isExternalShareCollaborationEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantFormsSettings).GetIsExternalShareCollaborationEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.formsSettings.isExternalShareResultEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantFormsSettings).GetIsExternalShareResultEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.formsSettings.isExternalShareTemplateEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantFormsSettings).GetIsExternalShareTemplateEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.formsSettings.isRecordIdentityByDefaultEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantFormsSettings).GetIsRecordIdentityByDefaultEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.formsSettings.isBingImageSearchEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantFormsSettings).GetIsBingImageSearchEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.tenant.formsSettings.isInOrgFormsPhishingScanEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftTenantFormsSettings).GetIsInOrgFormsPhishingScanEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.users.filter": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUsers).GetFilter()).ToDataRes(types.String)
	},
	"microsoft.users.search": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUsers).GetSearch()).ToDataRes(types.String)
	},
	"microsoft.users.list": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUsers).GetList()).ToDataRes(types.Array(types.Resource("microsoft.user")))
	},
	"microsoft.identityAndAccess.filter": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccess).GetFilter()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstances": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccess).GetRoleEligibilityScheduleInstances()).ToDataRes(types.Array(types.Resource("microsoft.identityAndAccess.roleEligibilityScheduleInstance")))
	},
	"microsoft.identityAndAccess.list": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccess).GetList()).ToDataRes(types.Array(types.Resource("microsoft.identityAndAccess.policy")))
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.principalId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetPrincipalId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.roleDefinitionId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetRoleDefinitionId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.directoryScopeId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetDirectoryScopeId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.appScopeId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetAppScopeId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.startDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetStartDateTime()).ToDataRes(types.Time)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.endDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetEndDateTime()).ToDataRes(types.Time)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.memberType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetMemberType()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.roleEligibilityScheduleId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).GetRoleEligibilityScheduleId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.isOrganizationDefault": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetIsOrganizationDefault()).ToDataRes(types.Bool)
	},
	"microsoft.identityAndAccess.policy.scopeId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetScopeId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.scopeType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetScopeType()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.lastModifiedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetLastModifiedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.identityAndAccess.policy.lastModifiedBy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetLastModifiedBy()).ToDataRes(types.Dict)
	},
	"microsoft.identityAndAccess.policy.rules": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicy).GetRules()).ToDataRes(types.Array(types.Resource("microsoft.identityAndAccess.policy.rule")))
	},
	"microsoft.identityAndAccess.policy.rule.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicyRule).GetId()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.rule.target": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicyRule).GetTarget()).ToDataRes(types.Resource("microsoft.identityAndAccess.policy.rule.target"))
	},
	"microsoft.identityAndAccess.policy.rule.target.caller": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).GetCaller()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.rule.target.enforcedSettings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).GetEnforcedSettings()).ToDataRes(types.Array(types.String))
	},
	"microsoft.identityAndAccess.policy.rule.target.inheritableSettings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).GetInheritableSettings()).ToDataRes(types.Array(types.String))
	},
	"microsoft.identityAndAccess.policy.rule.target.level": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).GetLevel()).ToDataRes(types.String)
	},
	"microsoft.identityAndAccess.policy.rule.target.operations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).GetOperations()).ToDataRes(types.Array(types.String))
	},
	"microsoft.user.assignedLicense.disabledPlans": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAssignedLicense).GetDisabledPlans()).ToDataRes(types.Array(types.String))
	},
	"microsoft.user.assignedLicense.skuId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAssignedLicense).GetSkuId()).ToDataRes(types.String)
	},
	"microsoft.user.licenseDetail.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserLicenseDetail).GetId()).ToDataRes(types.String)
	},
	"microsoft.user.licenseDetail.skuId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserLicenseDetail).GetSkuId()).ToDataRes(types.String)
	},
	"microsoft.user.licenseDetail.skuPartNumber": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserLicenseDetail).GetSkuPartNumber()).ToDataRes(types.String)
	},
	"microsoft.user.licenseDetail.servicePlans": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserLicenseDetail).GetServicePlans()).ToDataRes(types.Array(types.Resource("microsoft.user.licenseDetail.servicePlanInfo")))
	},
	"microsoft.user.licenseDetail.servicePlanInfo.appliesTo": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).GetAppliesTo()).ToDataRes(types.String)
	},
	"microsoft.user.licenseDetail.servicePlanInfo.provisioningStatus": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).GetProvisioningStatus()).ToDataRes(types.String)
	},
	"microsoft.user.licenseDetail.servicePlanInfo.servicePlanId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).GetServicePlanId()).ToDataRes(types.String)
	},
	"microsoft.user.licenseDetail.servicePlanInfo.servicePlanName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).GetServicePlanName()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.namedLocations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccess).GetNamedLocations()).ToDataRes(types.Resource("microsoft.conditionalAccess.namedLocations"))
	},
	"microsoft.conditionalAccess.policies": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccess).GetPolicies()).ToDataRes(types.Array(types.Resource("microsoft.conditionalAccess.policy")))
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccess).GetAuthenticationMethodsPolicy()).ToDataRes(types.Resource("microsoft.conditionalAccess.authenticationMethodsPolicy"))
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).GetId()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.lastModifiedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).GetLastModifiedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.policyVersion": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).GetPolicyVersion()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.authenticationMethodConfigurations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).GetAuthenticationMethodConfigurations()).ToDataRes(types.Array(types.Resource("microsoft.conditionalAccess.authenticationMethodConfiguration")))
	},
	"microsoft.conditionalAccess.authenticationMethodConfiguration.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration).GetId()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.authenticationMethodConfiguration.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration).GetState()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.namedLocations.ipLocations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessNamedLocations).GetIpLocations()).ToDataRes(types.Array(types.Resource("microsoft.conditionalAccess.ipNamedLocation")))
	},
	"microsoft.conditionalAccess.namedLocations.countryLocations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessNamedLocations).GetCountryLocations()).ToDataRes(types.Array(types.Resource("microsoft.conditionalAccess.countryNamedLocation")))
	},
	"microsoft.conditionalAccess.policy.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetId()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetState()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.conditionalAccess.policy.modifiedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetModifiedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.conditionalAccess.policy.conditions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetConditions()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.conditions"))
	},
	"microsoft.conditionalAccess.policy.grantControls": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetGrantControls()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.grantControls"))
	},
	"microsoft.conditionalAccess.policy.sessionControls": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetSessionControls()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.sessionControls"))
	},
	"microsoft.conditionalAccess.policy.templateId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicy).GetTemplateId()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.conditions.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetId()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.conditions.applications": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetApplications()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.conditions.applications"))
	},
	"microsoft.conditionalAccess.policy.conditions.authenticationFlows": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetAuthenticationFlows()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.conditions.authenticationFlows"))
	},
	"microsoft.conditionalAccess.policy.conditions.clientApplications": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetClientApplications()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.conditions.clientApplications"))
	},
	"microsoft.conditionalAccess.policy.conditions.clientAppTypes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetClientAppTypes()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.locations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetLocations()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.conditions.locations"))
	},
	"microsoft.conditionalAccess.policy.conditions.platforms": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetPlatforms()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.conditions.platforms"))
	},
	"microsoft.conditionalAccess.policy.conditions.servicePrincipalRiskLevels": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetServicePrincipalRiskLevels()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.signInRiskLevels": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetSignInRiskLevels()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.userRiskLevels": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetUserRiskLevels()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.users": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetUsers()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.conditions.users"))
	},
	"microsoft.conditionalAccess.policy.conditions.insiderRiskLevels": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditions).GetInsiderRiskLevels()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.conditions.authenticationFlows.transferMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows).GetTransferMethods()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).GetId()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.allowedCombinations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).GetAllowedCombinations()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.policyType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).GetPolicyType()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.requirementsSatisfied": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).GetRequirementsSatisfied()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.modifiedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).GetModifiedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency.authenticationType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency).GetAuthenticationType()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency.frequencyInterval": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency).GetFrequencyInterval()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency.isEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency).GetIsEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity.cloudAppSecurityType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity).GetCloudAppSecurityType()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity.isEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity).GetIsEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.conditionalAccess.policy.sessionControls.persistentBrowser.mode": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser).GetMode()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.sessionControls.persistentBrowser.isEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser).GetIsEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions.isEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions).GetIsEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.conditionalAccess.policy.conditions.clientApplications.excludeServicePrincipals": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsClientApplications).GetExcludeServicePrincipals()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.clientApplications.includeServicePrincipals": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsClientApplications).GetIncludeServicePrincipals()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.platforms.excludePlatforms": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsPlatforms).GetExcludePlatforms()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.platforms.includePlatforms": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsPlatforms).GetIncludePlatforms()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.applications.includeApplications": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsApplications).GetIncludeApplications()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.applications.excludeApplications": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsApplications).GetExcludeApplications()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.applications.includeUserActions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsApplications).GetIncludeUserActions()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.users.includeUsers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).GetIncludeUsers()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.users.excludeUsers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).GetExcludeUsers()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.users.includeGroups": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).GetIncludeGroups()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.users.excludeGroups": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).GetExcludeGroups()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.users.includeRoles": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).GetIncludeRoles()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.users.excludeRoles": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).GetExcludeRoles()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.locations.includeLocations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsLocations).GetIncludeLocations()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.conditions.locations.excludeLocations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyConditionsLocations).GetExcludeLocations()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.grantControls.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).GetId()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.grantControls.operator": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).GetOperator()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.grantControls.builtInControls": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).GetBuiltInControls()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).GetAuthenticationStrength()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.grantControls.authenticationStrength"))
	},
	"microsoft.conditionalAccess.policy.grantControls.customAuthenticationFactors": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).GetCustomAuthenticationFactors()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.grantControls.termsOfUse": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).GetTermsOfUse()).ToDataRes(types.Array(types.String))
	},
	"microsoft.conditionalAccess.policy.sessionControls.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControls).GetId()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControls).GetSignInFrequency()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.sessionControls.signInFrequency"))
	},
	"microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControls).GetCloudAppSecurity()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity"))
	},
	"microsoft.conditionalAccess.policy.sessionControls.persistentBrowser": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControls).GetPersistentBrowser()).ToDataRes(types.Dict)
	},
	"microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControls).GetApplicationEnforcedRestrictions()).ToDataRes(types.Resource("microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions"))
	},
	"microsoft.conditionalAccess.policy.sessionControls.secureSignInSession": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessPolicySessionControls).GetSecureSignInSession()).ToDataRes(types.Dict)
	},
	"microsoft.conditionalAccess.ipNamedLocation.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessIpNamedLocation).GetName()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.ipNamedLocation.trusted": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessIpNamedLocation).GetTrusted()).ToDataRes(types.Bool)
	},
	"microsoft.conditionalAccess.countryNamedLocation.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessCountryNamedLocation).GetName()).ToDataRes(types.String)
	},
	"microsoft.conditionalAccess.countryNamedLocation.lookupMethod": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftConditionalAccessCountryNamedLocation).GetLookupMethod()).ToDataRes(types.String)
	},
	"microsoft.user.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetId()).ToDataRes(types.String)
	},
	"microsoft.user.accountEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetAccountEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.user.city": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetCity()).ToDataRes(types.String)
	},
	"microsoft.user.companyName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetCompanyName()).ToDataRes(types.String)
	},
	"microsoft.user.country": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetCountry()).ToDataRes(types.String)
	},
	"microsoft.user.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.user.department": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetDepartment()).ToDataRes(types.String)
	},
	"microsoft.user.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.user.employeeId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetEmployeeId()).ToDataRes(types.String)
	},
	"microsoft.user.givenName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetGivenName()).ToDataRes(types.String)
	},
	"microsoft.user.jobTitle": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetJobTitle()).ToDataRes(types.String)
	},
	"microsoft.user.mail": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetMail()).ToDataRes(types.String)
	},
	"microsoft.user.mobilePhone": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetMobilePhone()).ToDataRes(types.String)
	},
	"microsoft.user.otherMails": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetOtherMails()).ToDataRes(types.Array(types.String))
	},
	"microsoft.user.officeLocation": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetOfficeLocation()).ToDataRes(types.String)
	},
	"microsoft.user.postalCode": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetPostalCode()).ToDataRes(types.String)
	},
	"microsoft.user.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetState()).ToDataRes(types.String)
	},
	"microsoft.user.streetAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetStreetAddress()).ToDataRes(types.String)
	},
	"microsoft.user.surname": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetSurname()).ToDataRes(types.String)
	},
	"microsoft.user.userPrincipalName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetUserPrincipalName()).ToDataRes(types.String)
	},
	"microsoft.user.userType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetUserType()).ToDataRes(types.String)
	},
	"microsoft.user.settings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetSettings()).ToDataRes(types.Dict)
	},
	"microsoft.user.job": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetJob()).ToDataRes(types.Dict)
	},
	"microsoft.user.contact": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetContact()).ToDataRes(types.Dict)
	},
	"microsoft.user.authMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetAuthMethods()).ToDataRes(types.Resource("microsoft.user.authenticationMethods"))
	},
	"microsoft.user.mfaEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetMfaEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.user.creationType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetCreationType()).ToDataRes(types.String)
	},
	"microsoft.user.identities": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetIdentities()).ToDataRes(types.Array(types.Resource("microsoft.user.identity")))
	},
	"microsoft.user.auditlog": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetAuditlog()).ToDataRes(types.Resource("microsoft.user.auditlog"))
	},
	"microsoft.user.assignedLicenses": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetAssignedLicenses()).ToDataRes(types.Array(types.Resource("microsoft.user.assignedLicense")))
	},
	"microsoft.user.licenseDetails": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetLicenseDetails()).ToDataRes(types.Array(types.Resource("microsoft.user.licenseDetail")))
	},
	"microsoft.user.authenticationRequirements": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUser).GetAuthenticationRequirements()).ToDataRes(types.Resource("microsoft.user.authenticationRequirements"))
	},
	"microsoft.user.authenticationRequirements.perUserMfaState": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationRequirements).GetPerUserMfaState()).ToDataRes(types.String)
	},
	"microsoft.user.auditlog.userId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuditlog).GetUserId()).ToDataRes(types.String)
	},
	"microsoft.user.auditlog.signins": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuditlog).GetSignins()).ToDataRes(types.Array(types.Resource("microsoft.user.signin")))
	},
	"microsoft.user.auditlog.lastInteractiveSignIn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuditlog).GetLastInteractiveSignIn()).ToDataRes(types.Resource("microsoft.user.signin"))
	},
	"microsoft.user.auditlog.lastNonInteractiveSignIn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuditlog).GetLastNonInteractiveSignIn()).ToDataRes(types.Resource("microsoft.user.signin"))
	},
	"microsoft.user.identity.issuerAssignedId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserIdentity).GetIssuerAssignedId()).ToDataRes(types.String)
	},
	"microsoft.user.identity.issuer": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserIdentity).GetIssuer()).ToDataRes(types.String)
	},
	"microsoft.user.identity.signInType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserIdentity).GetSignInType()).ToDataRes(types.String)
	},
	"microsoft.user.signin.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserSignin).GetId()).ToDataRes(types.String)
	},
	"microsoft.user.signin.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserSignin).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.user.signin.userId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserSignin).GetUserId()).ToDataRes(types.String)
	},
	"microsoft.user.signin.userDisplayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserSignin).GetUserDisplayName()).ToDataRes(types.String)
	},
	"microsoft.user.signin.clientAppUsed": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserSignin).GetClientAppUsed()).ToDataRes(types.String)
	},
	"microsoft.user.signin.appDisplayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserSignin).GetAppDisplayName()).ToDataRes(types.String)
	},
	"microsoft.user.signin.resourceDisplayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserSignin).GetResourceDisplayName()).ToDataRes(types.String)
	},
	"microsoft.user.signin.interactive": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserSignin).GetInteractive()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.count": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetCount()).ToDataRes(types.Int)
	},
	"microsoft.user.authenticationMethods.phoneMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetPhoneMethods()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.user.authenticationMethods.emailMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetEmailMethods()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.user.authenticationMethods.fido2Methods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetFido2Methods()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.user.authenticationMethods.softwareMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetSoftwareMethods()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.user.authenticationMethods.microsoftAuthenticator": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetMicrosoftAuthenticator()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.user.authenticationMethods.passwordMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetPasswordMethods()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.user.authenticationMethods.temporaryAccessPassMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetTemporaryAccessPassMethods()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.user.authenticationMethods.windowsHelloMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetWindowsHelloMethods()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.user.authenticationMethods.registrationDetails": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethods).GetRegistrationDetails()).ToDataRes(types.Resource("microsoft.user.authenticationMethods.userRegistrationDetails"))
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetId()).ToDataRes(types.String)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isAdmin": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetIsAdmin()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isMfaCapable": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetIsMfaCapable()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isMfaRegistered": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetIsMfaRegistered()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isPasswordlessCapable": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetIsPasswordlessCapable()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isSsprCapable": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetIsSsprCapable()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isSsprEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetIsSsprEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isSsprRegistered": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetIsSsprRegistered()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isSystemPreferredAuthenticationMethodEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetIsSystemPreferredAuthenticationMethodEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.lastUpdatedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetLastUpdatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.methodsRegistered": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetMethodsRegistered()).ToDataRes(types.Array(types.String))
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.systemPreferredAuthenticationMethods": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetSystemPreferredAuthenticationMethods()).ToDataRes(types.Array(types.String))
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.userDisplayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetUserDisplayName()).ToDataRes(types.String)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.userPreferredMethodForSecondaryAuthentication": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetUserPreferredMethodForSecondaryAuthentication()).ToDataRes(types.String)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.userPrincipalName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetUserPrincipalName()).ToDataRes(types.String)
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.userType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).GetUserType()).ToDataRes(types.String)
	},
	"microsoft.group.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetId()).ToDataRes(types.String)
	},
	"microsoft.group.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.group.securityEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetSecurityEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.group.mailEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetMailEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.group.mailNickname": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetMailNickname()).ToDataRes(types.String)
	},
	"microsoft.group.mail": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetMail()).ToDataRes(types.String)
	},
	"microsoft.group.visibility": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetVisibility()).ToDataRes(types.String)
	},
	"microsoft.group.members": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetMembers()).ToDataRes(types.Array(types.Resource("microsoft.user")))
	},
	"microsoft.group.groupTypes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetGroupTypes()).ToDataRes(types.Array(types.String))
	},
	"microsoft.group.membershipRule": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetMembershipRule()).ToDataRes(types.String)
	},
	"microsoft.group.membershipRuleProcessingState": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGroup).GetMembershipRuleProcessingState()).ToDataRes(types.String)
	},
	"microsoft.devices.filter": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevices).GetFilter()).ToDataRes(types.String)
	},
	"microsoft.devices.search": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevices).GetSearch()).ToDataRes(types.String)
	},
	"microsoft.devices.list": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevices).GetList()).ToDataRes(types.Array(types.Resource("microsoft.device")))
	},
	"microsoft.device.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetId()).ToDataRes(types.String)
	},
	"microsoft.device.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.device.deviceId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetDeviceId()).ToDataRes(types.String)
	},
	"microsoft.device.deviceCategory": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetDeviceCategory()).ToDataRes(types.String)
	},
	"microsoft.device.enrollmentProfileName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetEnrollmentProfileName()).ToDataRes(types.String)
	},
	"microsoft.device.enrollmentType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetEnrollmentType()).ToDataRes(types.String)
	},
	"microsoft.device.isCompliant": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetIsCompliant()).ToDataRes(types.Bool)
	},
	"microsoft.device.isManaged": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetIsManaged()).ToDataRes(types.Bool)
	},
	"microsoft.device.manufacturer": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetManufacturer()).ToDataRes(types.String)
	},
	"microsoft.device.isRooted": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetIsRooted()).ToDataRes(types.Bool)
	},
	"microsoft.device.mdmAppId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetMdmAppId()).ToDataRes(types.String)
	},
	"microsoft.device.model": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetModel()).ToDataRes(types.String)
	},
	"microsoft.device.operatingSystem": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetOperatingSystem()).ToDataRes(types.String)
	},
	"microsoft.device.operatingSystemVersion": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetOperatingSystemVersion()).ToDataRes(types.String)
	},
	"microsoft.device.physicalIds": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetPhysicalIds()).ToDataRes(types.Array(types.String))
	},
	"microsoft.device.registrationDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetRegistrationDateTime()).ToDataRes(types.Time)
	},
	"microsoft.device.systemLabels": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetSystemLabels()).ToDataRes(types.Array(types.String))
	},
	"microsoft.device.trustType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevice).GetTrustType()).ToDataRes(types.String)
	},
	"microsoft.domain.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetId()).ToDataRes(types.String)
	},
	"microsoft.domain.authenticationType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetAuthenticationType()).ToDataRes(types.String)
	},
	"microsoft.domain.availabilityStatus": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetAvailabilityStatus()).ToDataRes(types.String)
	},
	"microsoft.domain.isAdminManaged": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetIsAdminManaged()).ToDataRes(types.Bool)
	},
	"microsoft.domain.isDefault": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetIsDefault()).ToDataRes(types.Bool)
	},
	"microsoft.domain.isInitial": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetIsInitial()).ToDataRes(types.Bool)
	},
	"microsoft.domain.isRoot": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetIsRoot()).ToDataRes(types.Bool)
	},
	"microsoft.domain.isVerified": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetIsVerified()).ToDataRes(types.Bool)
	},
	"microsoft.domain.passwordNotificationWindowInDays": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetPasswordNotificationWindowInDays()).ToDataRes(types.Int)
	},
	"microsoft.domain.passwordValidityPeriodInDays": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetPasswordValidityPeriodInDays()).ToDataRes(types.Int)
	},
	"microsoft.domain.supportedServices": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetSupportedServices()).ToDataRes(types.Array(types.String))
	},
	"microsoft.domain.serviceConfigurationRecords": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomain).GetServiceConfigurationRecords()).ToDataRes(types.Array(types.Resource("microsoft.domaindnsrecord")))
	},
	"microsoft.domaindnsrecord.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomaindnsrecord).GetId()).ToDataRes(types.String)
	},
	"microsoft.domaindnsrecord.isOptional": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomaindnsrecord).GetIsOptional()).ToDataRes(types.Bool)
	},
	"microsoft.domaindnsrecord.label": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomaindnsrecord).GetLabel()).ToDataRes(types.String)
	},
	"microsoft.domaindnsrecord.recordType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomaindnsrecord).GetRecordType()).ToDataRes(types.String)
	},
	"microsoft.domaindnsrecord.supportedService": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomaindnsrecord).GetSupportedService()).ToDataRes(types.String)
	},
	"microsoft.domaindnsrecord.ttl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomaindnsrecord).GetTtl()).ToDataRes(types.Int)
	},
	"microsoft.domaindnsrecord.properties": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDomaindnsrecord).GetProperties()).ToDataRes(types.Dict)
	},
	"microsoft.application.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetId()).ToDataRes(types.String)
	},
	"microsoft.application.appId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetAppId()).ToDataRes(types.String)
	},
	"microsoft.application.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetName()).ToDataRes(types.String)
	},
	"microsoft.application.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.application.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.application.notes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetNotes()).ToDataRes(types.String)
	},
	"microsoft.application.tags": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetTags()).ToDataRes(types.Array(types.String))
	},
	"microsoft.application.applicationTemplateId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetApplicationTemplateId()).ToDataRes(types.String)
	},
	"microsoft.application.disabledByMicrosoftStatus": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetDisabledByMicrosoftStatus()).ToDataRes(types.String)
	},
	"microsoft.application.groupMembershipClaims": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetGroupMembershipClaims()).ToDataRes(types.String)
	},
	"microsoft.application.createdAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetCreatedAt()).ToDataRes(types.Time)
	},
	"microsoft.application.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.application.identifierUris": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetIdentifierUris()).ToDataRes(types.Array(types.String))
	},
	"microsoft.application.publisherDomain": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetPublisherDomain()).ToDataRes(types.String)
	},
	"microsoft.application.signInAudience": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetSignInAudience()).ToDataRes(types.String)
	},
	"microsoft.application.info": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetInfo()).ToDataRes(types.Dict)
	},
	"microsoft.application.api": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetApi()).ToDataRes(types.Dict)
	},
	"microsoft.application.web": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetWeb()).ToDataRes(types.Dict)
	},
	"microsoft.application.spa": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetSpa()).ToDataRes(types.Dict)
	},
	"microsoft.application.secrets": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetSecrets()).ToDataRes(types.Array(types.Resource("microsoft.passwordCredential")))
	},
	"microsoft.application.certificates": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetCertificates()).ToDataRes(types.Array(types.Resource("microsoft.keyCredential")))
	},
	"microsoft.application.hasExpiredCredentials": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetHasExpiredCredentials()).ToDataRes(types.Bool)
	},
	"microsoft.application.owners": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetOwners()).ToDataRes(types.Array(types.Resource("microsoft.user")))
	},
	"microsoft.application.servicePrincipal": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetServicePrincipal()).ToDataRes(types.Resource("microsoft.serviceprincipal"))
	},
	"microsoft.application.isDeviceOnlyAuthSupported": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetIsDeviceOnlyAuthSupported()).ToDataRes(types.Bool)
	},
	"microsoft.application.isFallbackPublicClient": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetIsFallbackPublicClient()).ToDataRes(types.Bool)
	},
	"microsoft.application.nativeAuthenticationApisEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetNativeAuthenticationApisEnabled()).ToDataRes(types.String)
	},
	"microsoft.application.serviceManagementReference": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetServiceManagementReference()).ToDataRes(types.String)
	},
	"microsoft.application.tokenEncryptionKeyId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetTokenEncryptionKeyId()).ToDataRes(types.String)
	},
	"microsoft.application.samlMetadataUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetSamlMetadataUrl()).ToDataRes(types.String)
	},
	"microsoft.application.defaultRedirectUri": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetDefaultRedirectUri()).ToDataRes(types.String)
	},
	"microsoft.application.certification": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetCertification()).ToDataRes(types.Dict)
	},
	"microsoft.application.optionalClaims": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetOptionalClaims()).ToDataRes(types.Dict)
	},
	"microsoft.application.servicePrincipalLockConfiguration": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetServicePrincipalLockConfiguration()).ToDataRes(types.Dict)
	},
	"microsoft.application.requestSignatureVerification": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetRequestSignatureVerification()).ToDataRes(types.Dict)
	},
	"microsoft.application.parentalControlSettings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetParentalControlSettings()).ToDataRes(types.Dict)
	},
	"microsoft.application.publicClient": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetPublicClient()).ToDataRes(types.Dict)
	},
	"microsoft.application.appRoles": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplication).GetAppRoles()).ToDataRes(types.Array(types.Resource("microsoft.application.role")))
	},
	"microsoft.application.role.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationRole).GetId()).ToDataRes(types.String)
	},
	"microsoft.application.role.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationRole).GetName()).ToDataRes(types.String)
	},
	"microsoft.application.role.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationRole).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.application.role.value": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationRole).GetValue()).ToDataRes(types.String)
	},
	"microsoft.application.role.allowedMemberTypes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationRole).GetAllowedMemberTypes()).ToDataRes(types.Array(types.String))
	},
	"microsoft.application.role.isEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationRole).GetIsEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.keyCredential.keyId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftKeyCredential).GetKeyId()).ToDataRes(types.String)
	},
	"microsoft.keyCredential.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftKeyCredential).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.keyCredential.thumbprint": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftKeyCredential).GetThumbprint()).ToDataRes(types.String)
	},
	"microsoft.keyCredential.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftKeyCredential).GetType()).ToDataRes(types.String)
	},
	"microsoft.keyCredential.usage": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftKeyCredential).GetUsage()).ToDataRes(types.String)
	},
	"microsoft.keyCredential.expires": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftKeyCredential).GetExpires()).ToDataRes(types.Time)
	},
	"microsoft.keyCredential.expired": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftKeyCredential).GetExpired()).ToDataRes(types.Bool)
	},
	"microsoft.passwordCredential.keyId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPasswordCredential).GetKeyId()).ToDataRes(types.String)
	},
	"microsoft.passwordCredential.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPasswordCredential).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.passwordCredential.hint": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPasswordCredential).GetHint()).ToDataRes(types.String)
	},
	"microsoft.passwordCredential.expires": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPasswordCredential).GetExpires()).ToDataRes(types.Time)
	},
	"microsoft.passwordCredential.expired": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPasswordCredential).GetExpired()).ToDataRes(types.Bool)
	},
	"microsoft.serviceprincipal.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetId()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetType()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetName()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.appId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetAppId()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.appOwnerOrganizationId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetAppOwnerOrganizationId()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.tags": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetTags()).ToDataRes(types.Array(types.String))
	},
	"microsoft.serviceprincipal.enabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.serviceprincipal.homepageUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetHomepageUrl()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.termsOfServiceUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetTermsOfServiceUrl()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.replyUrls": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetReplyUrls()).ToDataRes(types.Array(types.String))
	},
	"microsoft.serviceprincipal.assignmentRequired": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetAssignmentRequired()).ToDataRes(types.Bool)
	},
	"microsoft.serviceprincipal.visibleToUsers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetVisibleToUsers()).ToDataRes(types.Bool)
	},
	"microsoft.serviceprincipal.notes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetNotes()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.assignments": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetAssignments()).ToDataRes(types.Array(types.Resource("microsoft.serviceprincipal.assignment")))
	},
	"microsoft.serviceprincipal.applicationTemplateId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetApplicationTemplateId()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.verifiedPublisher": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetVerifiedPublisher()).ToDataRes(types.Dict)
	},
	"microsoft.serviceprincipal.loginUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetLoginUrl()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.logoutUrl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetLogoutUrl()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.servicePrincipalNames": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetServicePrincipalNames()).ToDataRes(types.Array(types.String))
	},
	"microsoft.serviceprincipal.signInAudience": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetSignInAudience()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.preferredSingleSignOnMode": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetPreferredSingleSignOnMode()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.notificationEmailAddresses": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetNotificationEmailAddresses()).ToDataRes(types.Array(types.String))
	},
	"microsoft.serviceprincipal.appRoleAssignmentRequired": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetAppRoleAssignmentRequired()).ToDataRes(types.Bool)
	},
	"microsoft.serviceprincipal.accountEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetAccountEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.serviceprincipal.isFirstParty": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetIsFirstParty()).ToDataRes(types.Bool)
	},
	"microsoft.serviceprincipal.appRoles": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetAppRoles()).ToDataRes(types.Array(types.Resource("microsoft.application.role")))
	},
	"microsoft.serviceprincipal.permissions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipal).GetPermissions()).ToDataRes(types.Array(types.Resource("microsoft.application.permission")))
	},
	"microsoft.serviceprincipal.assignment.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipalAssignment).GetId()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.assignment.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipalAssignment).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.serviceprincipal.assignment.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftServiceprincipalAssignment).GetType()).ToDataRes(types.String)
	},
	"microsoft.application.permission.appId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationPermission).GetAppId()).ToDataRes(types.String)
	},
	"microsoft.application.permission.appName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationPermission).GetAppName()).ToDataRes(types.String)
	},
	"microsoft.application.permission.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationPermission).GetId()).ToDataRes(types.String)
	},
	"microsoft.application.permission.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationPermission).GetName()).ToDataRes(types.String)
	},
	"microsoft.application.permission.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationPermission).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.application.permission.type": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationPermission).GetType()).ToDataRes(types.String)
	},
	"microsoft.application.permission.status": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftApplicationPermission).GetStatus()).ToDataRes(types.String)
	},
	"microsoft.security.secureScores": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurity).GetSecureScores()).ToDataRes(types.Array(types.Resource("microsoft.security.securityscore")))
	},
	"microsoft.security.latestSecureScores": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurity).GetLatestSecureScores()).ToDataRes(types.Resource("microsoft.security.securityscore"))
	},
	"microsoft.security.riskyUsers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurity).GetRiskyUsers()).ToDataRes(types.Array(types.Resource("microsoft.security.riskyUser")))
	},
	"microsoft.security.securityscore.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetId()).ToDataRes(types.String)
	},
	"microsoft.security.securityscore.activeUserCount": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetActiveUserCount()).ToDataRes(types.Int)
	},
	"microsoft.security.securityscore.averageComparativeScores": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetAverageComparativeScores()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.security.securityscore.azureTenantId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetAzureTenantId()).ToDataRes(types.String)
	},
	"microsoft.security.securityscore.controlScores": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetControlScores()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.security.securityscore.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.security.securityscore.currentScore": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetCurrentScore()).ToDataRes(types.Float)
	},
	"microsoft.security.securityscore.enabledServices": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetEnabledServices()).ToDataRes(types.Array(types.String))
	},
	"microsoft.security.securityscore.licensedUserCount": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetLicensedUserCount()).ToDataRes(types.Int)
	},
	"microsoft.security.securityscore.maxScore": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetMaxScore()).ToDataRes(types.Float)
	},
	"microsoft.security.securityscore.vendorInformation": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecuritySecurityscore).GetVendorInformation()).ToDataRes(types.Dict)
	},
	"microsoft.security.riskyUser.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurityRiskyUser).GetId()).ToDataRes(types.String)
	},
	"microsoft.security.riskyUser.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurityRiskyUser).GetName()).ToDataRes(types.String)
	},
	"microsoft.security.riskyUser.principalName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurityRiskyUser).GetPrincipalName()).ToDataRes(types.String)
	},
	"microsoft.security.riskyUser.user": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurityRiskyUser).GetUser()).ToDataRes(types.Resource("microsoft.user"))
	},
	"microsoft.security.riskyUser.riskDetail": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurityRiskyUser).GetRiskDetail()).ToDataRes(types.String)
	},
	"microsoft.security.riskyUser.riskLevel": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurityRiskyUser).GetRiskLevel()).ToDataRes(types.String)
	},
	"microsoft.security.riskyUser.riskState": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurityRiskyUser).GetRiskState()).ToDataRes(types.String)
	},
	"microsoft.security.riskyUser.lastUpdatedAt": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftSecurityRiskyUser).GetLastUpdatedAt()).ToDataRes(types.Time)
	},
	"microsoft.policies.authorizationPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPolicies).GetAuthorizationPolicy()).ToDataRes(types.Dict)
	},
	"microsoft.policies.identitySecurityDefaultsEnforcementPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPolicies).GetIdentitySecurityDefaultsEnforcementPolicy()).ToDataRes(types.Dict)
	},
	"microsoft.policies.adminConsentRequestPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPolicies).GetAdminConsentRequestPolicy()).ToDataRes(types.Resource("microsoft.adminConsentRequestPolicy"))
	},
	"microsoft.policies.permissionGrantPolicies": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPolicies).GetPermissionGrantPolicies()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.policies.consentPolicySettings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPolicies).GetConsentPolicySettings()).ToDataRes(types.Dict)
	},
	"microsoft.policies.authenticationMethodsPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPolicies).GetAuthenticationMethodsPolicy()).ToDataRes(types.Resource("microsoft.policies.authenticationMethodsPolicy"))
	},
	"microsoft.adminConsentRequestPolicy.isEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftAdminConsentRequestPolicy).GetIsEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.adminConsentRequestPolicy.notifyReviewers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftAdminConsentRequestPolicy).GetNotifyReviewers()).ToDataRes(types.Bool)
	},
	"microsoft.adminConsentRequestPolicy.remindersEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftAdminConsentRequestPolicy).GetRemindersEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.adminConsentRequestPolicy.requestDurationInDays": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftAdminConsentRequestPolicy).GetRequestDurationInDays()).ToDataRes(types.Int)
	},
	"microsoft.adminConsentRequestPolicy.reviewers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftAdminConsentRequestPolicy).GetReviewers()).ToDataRes(types.Array(types.Resource("microsoft.graph.accessReviewReviewerScope")))
	},
	"microsoft.adminConsentRequestPolicy.version": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftAdminConsentRequestPolicy).GetVersion()).ToDataRes(types.Int)
	},
	"microsoft.graph.accessReviewReviewerScope.query": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGraphAccessReviewReviewerScope).GetQuery()).ToDataRes(types.String)
	},
	"microsoft.graph.accessReviewReviewerScope.queryRoot": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGraphAccessReviewReviewerScope).GetQueryRoot()).ToDataRes(types.String)
	},
	"microsoft.graph.accessReviewReviewerScope.queryType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftGraphAccessReviewReviewerScope).GetQueryType()).ToDataRes(types.String)
	},
	"microsoft.policies.authenticationMethodsPolicy.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).GetId()).ToDataRes(types.String)
	},
	"microsoft.policies.authenticationMethodsPolicy.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.policies.authenticationMethodsPolicy.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.policies.authenticationMethodsPolicy.lastModifiedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).GetLastModifiedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.policies.authenticationMethodsPolicy.policyVersion": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).GetPolicyVersion()).ToDataRes(types.String)
	},
	"microsoft.policies.authenticationMethodsPolicy.authenticationMethodConfigurations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).GetAuthenticationMethodConfigurations()).ToDataRes(types.Array(types.Resource("microsoft.policies.authenticationMethodConfiguration")))
	},
	"microsoft.policies.authenticationMethodConfiguration.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodConfiguration).GetId()).ToDataRes(types.String)
	},
	"microsoft.policies.authenticationMethodConfiguration.state": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodConfiguration).GetState()).ToDataRes(types.String)
	},
	"microsoft.policies.authenticationMethodConfiguration.excludeTargets": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftPoliciesAuthenticationMethodConfiguration).GetExcludeTargets()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.roles.filter": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRoles).GetFilter()).ToDataRes(types.String)
	},
	"microsoft.roles.search": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRoles).GetSearch()).ToDataRes(types.String)
	},
	"microsoft.roles.list": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRoles).GetList()).ToDataRes(types.Array(types.Resource("microsoft.rolemanagement.roledefinition")))
	},
	"microsoft.rolemanagement.roleDefinitions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagement).GetRoleDefinitions()).ToDataRes(types.Resource("microsoft.roles"))
	},
	"microsoft.rolemanagement.roledefinition.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetId()).ToDataRes(types.String)
	},
	"microsoft.rolemanagement.roledefinition.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.rolemanagement.roledefinition.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.rolemanagement.roledefinition.isBuiltIn": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetIsBuiltIn()).ToDataRes(types.Bool)
	},
	"microsoft.rolemanagement.roledefinition.isEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetIsEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.rolemanagement.roledefinition.rolePermissions": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetRolePermissions()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.rolemanagement.roledefinition.templateId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetTemplateId()).ToDataRes(types.String)
	},
	"microsoft.rolemanagement.roledefinition.version": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetVersion()).ToDataRes(types.String)
	},
	"microsoft.rolemanagement.roledefinition.assignments": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoledefinition).GetAssignments()).ToDataRes(types.Array(types.Resource("microsoft.rolemanagement.roleassignment")))
	},
	"microsoft.rolemanagement.roleassignment.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoleassignment).GetId()).ToDataRes(types.String)
	},
	"microsoft.rolemanagement.roleassignment.roleDefinitionId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoleassignment).GetRoleDefinitionId()).ToDataRes(types.String)
	},
	"microsoft.rolemanagement.roleassignment.principalId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoleassignment).GetPrincipalId()).ToDataRes(types.String)
	},
	"microsoft.rolemanagement.roleassignment.principal": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftRolemanagementRoleassignment).GetPrincipal()).ToDataRes(types.Dict)
	},
	"microsoft.devicemanagement.managedDevices": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagement).GetManagedDevices()).ToDataRes(types.Array(types.Resource("microsoft.devicemanagement.manageddevice")))
	},
	"microsoft.devicemanagement.deviceConfigurations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagement).GetDeviceConfigurations()).ToDataRes(types.Array(types.Resource("microsoft.devicemanagement.deviceconfiguration")))
	},
	"microsoft.devicemanagement.deviceCompliancePolicies": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagement).GetDeviceCompliancePolicies()).ToDataRes(types.Array(types.Resource("microsoft.devicemanagement.devicecompliancepolicy")))
	},
	"microsoft.devicemanagement.deviceEnrollmentConfigurations": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagement).GetDeviceEnrollmentConfigurations()).ToDataRes(types.Array(types.Resource("microsoft.devicemanagement.deviceEnrollmentConfiguration")))
	},
	"microsoft.devicemanagement.settings": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagement).GetSettings()).ToDataRes(types.Resource("microsoft.devicemanagement.settings"))
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).GetId()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.priority": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).GetPriority()).ToDataRes(types.Int)
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.lastModifiedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).GetLastModifiedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.version": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).GetVersion()).ToDataRes(types.Int)
	},
	"microsoft.devicemanagement.manageddevice.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetId()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.userId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetUserId()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.name": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetName()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.operatingSystem": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetOperatingSystem()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.jailBroken": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetJailBroken()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.osVersion": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetOsVersion()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.easActivated": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetEasActivated()).ToDataRes(types.Bool)
	},
	"microsoft.devicemanagement.manageddevice.easDeviceId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetEasDeviceId()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.azureADRegistered": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetAzureADRegistered()).ToDataRes(types.Bool)
	},
	"microsoft.devicemanagement.manageddevice.emailAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetEmailAddress()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.azureActiveDirectoryDeviceId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetAzureActiveDirectoryDeviceId()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.deviceCategoryDisplayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetDeviceCategoryDisplayName()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.isSupervised": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetIsSupervised()).ToDataRes(types.Bool)
	},
	"microsoft.devicemanagement.manageddevice.isEncrypted": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetIsEncrypted()).ToDataRes(types.Bool)
	},
	"microsoft.devicemanagement.manageddevice.userPrincipalName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetUserPrincipalName()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.model": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetModel()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.manufacturer": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetManufacturer()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.imei": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetImei()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.serialNumber": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetSerialNumber()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.androidSecurityPatchLevel": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetAndroidSecurityPatchLevel()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.userDisplayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetUserDisplayName()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.wiFiMacAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetWiFiMacAddress()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.meid": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetMeid()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.iccid": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetIccid()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.udid": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetUdid()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.notes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetNotes()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.ethernetMacAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetEthernetMacAddress()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.enrollmentProfileName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetEnrollmentProfileName()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.manageddevice.windowsProtectionState": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementManageddevice).GetWindowsProtectionState()).ToDataRes(types.Dict)
	},
	"microsoft.devicemanagement.deviceconfiguration.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).GetId()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.deviceconfiguration.lastModifiedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).GetLastModifiedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.devicemanagement.deviceconfiguration.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.devicemanagement.deviceconfiguration.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.deviceconfiguration.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.deviceconfiguration.version": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).GetVersion()).ToDataRes(types.Int)
	},
	"microsoft.devicemanagement.deviceconfiguration.properties": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).GetProperties()).ToDataRes(types.Dict)
	},
	"microsoft.devicemanagement.devicecompliancepolicy.id": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).GetId()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.devicecompliancepolicy.createdDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).GetCreatedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.devicemanagement.devicecompliancepolicy.description": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).GetDescription()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.devicecompliancepolicy.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).GetDisplayName()).ToDataRes(types.String)
	},
	"microsoft.devicemanagement.devicecompliancepolicy.lastModifiedDateTime": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).GetLastModifiedDateTime()).ToDataRes(types.Time)
	},
	"microsoft.devicemanagement.devicecompliancepolicy.version": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).GetVersion()).ToDataRes(types.Int)
	},
	"microsoft.devicemanagement.devicecompliancepolicy.assignments": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).GetAssignments()).ToDataRes(types.Array(types.Dict))
	},
	"microsoft.devicemanagement.devicecompliancepolicy.properties": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).GetProperties()).ToDataRes(types.Dict)
	},
	"microsoft.devicemanagement.settings.secureByDefault": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementSettings).GetSecureByDefault()).ToDataRes(types.Bool)
	},
	"microsoft.devicemanagement.settings.isScheduledActionEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementSettings).GetIsScheduledActionEnabled()).ToDataRes(types.Bool)
	},
	"microsoft.devicemanagement.settings.deviceComplianceCheckinThresholdDays": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMicrosoftDevicemanagementSettings).GetDeviceComplianceCheckinThresholdDays()).ToDataRes(types.Int)
	},
	"ms365.exchangeonline.malwareFilterPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetMalwareFilterPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.hostedOutboundSpamFilterPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetHostedOutboundSpamFilterPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.transportRule": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetTransportRule()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.remoteDomain": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetRemoteDomain()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.safeLinksPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetSafeLinksPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.safeAttachmentPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetSafeAttachmentPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.organizationConfig": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetOrganizationConfig()).ToDataRes(types.Dict)
	},
	"ms365.exchangeonline.authenticationPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetAuthenticationPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.antiPhishPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetAntiPhishPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.dkimSigningConfig": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetDkimSigningConfig()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.owaMailboxPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetOwaMailboxPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.adminAuditLogConfig": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetAdminAuditLogConfig()).ToDataRes(types.Dict)
	},
	"ms365.exchangeonline.phishFilterPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetPhishFilterPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.mailbox": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetMailbox()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.atpPolicyForO365": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetAtpPolicyForO365()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.sharingPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetSharingPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.roleAssignmentPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetRoleAssignmentPolicy()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.externalInOutlook": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetExternalInOutlook()).ToDataRes(types.Array(types.Resource("ms365.exchangeonline.externalSender")))
	},
	"ms365.exchangeonline.sharedMailboxes": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetSharedMailboxes()).ToDataRes(types.Array(types.Resource("ms365.exchangeonline.exoMailbox")))
	},
	"ms365.exchangeonline.teamsProtectionPolicies": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetTeamsProtectionPolicies()).ToDataRes(types.Array(types.Resource("ms365.exchangeonline.teamsProtectionPolicy")))
	},
	"ms365.exchangeonline.reportSubmissionPolicies": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetReportSubmissionPolicies()).ToDataRes(types.Array(types.Resource("ms365.exchangeonline.reportSubmissionPolicy")))
	},
	"ms365.exchangeonline.mailboxesWithAudit": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetMailboxesWithAudit()).ToDataRes(types.Array(types.Resource("ms365.exchangeonline.mailbox")))
	},
	"ms365.exchangeonline.transportConfig": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetTransportConfig()).ToDataRes(types.Dict)
	},
	"ms365.exchangeonline.securityAndCompliance": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Exchangeonline).GetSecurityAndCompliance()).ToDataRes(types.Resource("ms365.exchangeonline.securityAndCompliance"))
	},
	"ms365.exchangeonline.securityAndCompliance.dlpCompliancePolicies": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineSecurityAndCompliance).GetDlpCompliancePolicies()).ToDataRes(types.Array(types.Dict))
	},
	"ms365.exchangeonline.teamsProtectionPolicy.zapEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineTeamsProtectionPolicy).GetZapEnabled()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.teamsProtectionPolicy.isValid": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineTeamsProtectionPolicy).GetIsValid()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportJunkToCustomizedAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).GetReportJunkToCustomizedAddress()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportNotJunkToCustomizedAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).GetReportNotJunkToCustomizedAddress()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportPhishToCustomizedAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).GetReportPhishToCustomizedAddress()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportJunkAddresses": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).GetReportJunkAddresses()).ToDataRes(types.Array(types.String))
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportNotJunkAddresses": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).GetReportNotJunkAddresses()).ToDataRes(types.Array(types.String))
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportPhishAddresses": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).GetReportPhishAddresses()).ToDataRes(types.Array(types.String))
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportChatMessageEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).GetReportChatMessageEnabled()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportChatMessageToCustomizedAddressEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).GetReportChatMessageToCustomizedAddressEnabled()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.externalSender.identity": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineExternalSender).GetIdentity()).ToDataRes(types.String)
	},
	"ms365.exchangeonline.externalSender.allowList": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineExternalSender).GetAllowList()).ToDataRes(types.Array(types.String))
	},
	"ms365.exchangeonline.externalSender.enabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineExternalSender).GetEnabled()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.exoMailbox.identity": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineExoMailbox).GetIdentity()).ToDataRes(types.String)
	},
	"ms365.exchangeonline.exoMailbox.user": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineExoMailbox).GetUser()).ToDataRes(types.Resource("microsoft.user"))
	},
	"ms365.exchangeonline.exoMailbox.externalDirectoryObjectId": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineExoMailbox).GetExternalDirectoryObjectId()).ToDataRes(types.String)
	},
	"ms365.exchangeonline.mailbox.identity": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetIdentity()).ToDataRes(types.String)
	},
	"ms365.exchangeonline.mailbox.displayName": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetDisplayName()).ToDataRes(types.String)
	},
	"ms365.exchangeonline.mailbox.primarySmtpAddress": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetPrimarySmtpAddress()).ToDataRes(types.String)
	},
	"ms365.exchangeonline.mailbox.recipientTypeDetails": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetRecipientTypeDetails()).ToDataRes(types.String)
	},
	"ms365.exchangeonline.mailbox.auditEnabled": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetAuditEnabled()).ToDataRes(types.Bool)
	},
	"ms365.exchangeonline.mailbox.auditAdmin": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetAuditAdmin()).ToDataRes(types.Array(types.String))
	},
	"ms365.exchangeonline.mailbox.auditDelegate": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetAuditDelegate()).ToDataRes(types.Array(types.String))
	},
	"ms365.exchangeonline.mailbox.auditOwner": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetAuditOwner()).ToDataRes(types.Array(types.String))
	},
	"ms365.exchangeonline.mailbox.auditLogAgeLimit": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365ExchangeonlineMailbox).GetAuditLogAgeLimit()).ToDataRes(types.String)
	},
	"ms365.sharepointonline.spoTenant": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Sharepointonline).GetSpoTenant()).ToDataRes(types.Dict)
	},
	"ms365.sharepointonline.spoTenantSyncClientRestriction": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Sharepointonline).GetSpoTenantSyncClientRestriction()).ToDataRes(types.Dict)
	},
	"ms365.sharepointonline.spoSites": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Sharepointonline).GetSpoSites()).ToDataRes(types.Array(types.Resource("ms365.sharepointonline.site")))
	},
	"ms365.sharepointonline.defaultLinkPermission": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Sharepointonline).GetDefaultLinkPermission()).ToDataRes(types.String)
	},
	"ms365.sharepointonline.site.url": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365SharepointonlineSite).GetUrl()).ToDataRes(types.String)
	},
	"ms365.sharepointonline.site.denyAddAndCustomizePages": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365SharepointonlineSite).GetDenyAddAndCustomizePages()).ToDataRes(types.Bool)
	},
	"ms365.teams.csTeamsClientConfiguration": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Teams).GetCsTeamsClientConfiguration()).ToDataRes(types.Dict)
	},
	"ms365.teams.csTenantFederationConfiguration": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Teams).GetCsTenantFederationConfiguration()).ToDataRes(types.Resource("ms365.teams.tenantFederationConfig"))
	},
	"ms365.teams.csTeamsMeetingPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Teams).GetCsTeamsMeetingPolicy()).ToDataRes(types.Resource("ms365.teams.teamsMeetingPolicyConfig"))
	},
	"ms365.teams.csTeamsMessagingPolicy": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365Teams).GetCsTeamsMessagingPolicy()).ToDataRes(types.Resource("ms365.teams.teamsMessagingPolicyConfig"))
	},
	"ms365.teams.tenantFederationConfig.identity": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetIdentity()).ToDataRes(types.String)
	},
	"ms365.teams.tenantFederationConfig.blockedDomains": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetBlockedDomains()).ToDataRes(types.Dict)
	},
	"ms365.teams.tenantFederationConfig.allowedDomains": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetAllowedDomains()).ToDataRes(types.Array(types.String))
	},
	"ms365.teams.tenantFederationConfig.allowFederatedUsers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetAllowFederatedUsers()).ToDataRes(types.Bool)
	},
	"ms365.teams.tenantFederationConfig.allowPublicUsers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetAllowPublicUsers()).ToDataRes(types.Bool)
	},
	"ms365.teams.tenantFederationConfig.allowTeamsConsumer": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetAllowTeamsConsumer()).ToDataRes(types.Bool)
	},
	"ms365.teams.tenantFederationConfig.allowTeamsConsumerInbound": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetAllowTeamsConsumerInbound()).ToDataRes(types.Bool)
	},
	"ms365.teams.tenantFederationConfig.treatDiscoveredPartnersAsUnverified": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetTreatDiscoveredPartnersAsUnverified()).ToDataRes(types.Bool)
	},
	"ms365.teams.tenantFederationConfig.sharedSipAddressSpace": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetSharedSipAddressSpace()).ToDataRes(types.Bool)
	},
	"ms365.teams.tenantFederationConfig.restrictTeamsConsumerToExternalUserProfiles": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTenantFederationConfig).GetRestrictTeamsConsumerToExternalUserProfiles()).ToDataRes(types.Bool)
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowAnonymousUsersToJoinMeeting": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetAllowAnonymousUsersToJoinMeeting()).ToDataRes(types.Bool)
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowAnonymousUsersToStartMeeting": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetAllowAnonymousUsersToStartMeeting()).ToDataRes(types.Bool)
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowExternalNonTrustedMeetingChat": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetAllowExternalNonTrustedMeetingChat()).ToDataRes(types.Bool)
	},
	"ms365.teams.teamsMeetingPolicyConfig.autoAdmittedUsers": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetAutoAdmittedUsers()).ToDataRes(types.String)
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowPSTNUsersToBypassLobby": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetAllowPSTNUsersToBypassLobby()).ToDataRes(types.Bool)
	},
	"ms365.teams.teamsMeetingPolicyConfig.meetingChatEnabledType": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetMeetingChatEnabledType()).ToDataRes(types.String)
	},
	"ms365.teams.teamsMeetingPolicyConfig.designatedPresenterRoleMode": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetDesignatedPresenterRoleMode()).ToDataRes(types.String)
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowExternalParticipantGiveRequestControl": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetAllowExternalParticipantGiveRequestControl()).ToDataRes(types.Bool)
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowSecurityEndUserReporting": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetAllowSecurityEndUserReporting()).ToDataRes(types.Bool)
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowCloudRecordingForCalls": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).GetAllowCloudRecordingForCalls()).ToDataRes(types.Bool)
	},
	"ms365.teams.teamsMessagingPolicyConfig.allowSecurityEndUserReporting": func(r plugin.Resource) *plugin.DataRes {
		return (r.(*mqlMs365TeamsTeamsMessagingPolicyConfig).GetAllowSecurityEndUserReporting()).ToDataRes(types.Bool)
	},
}

func GetData(resource plugin.Resource, field string, args map[string]*llx.RawData) *plugin.DataRes {
	f, ok := getDataFields[resource.MqlName()+"."+field]
	if !ok {
		return &plugin.DataRes{Error: "cannot find '" + field + "' in resource '" + resource.MqlName() + "'"}
	}

	return f(resource)
}

var setDataFields = map[string]func(r plugin.Resource, v *llx.RawData) bool {
	"microsoft.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoft).__id, ok = v.Value.(string)
			return
		},
	"microsoft.organizations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).Organizations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.users": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).Users, ok = plugin.RawToTValue[*mqlMicrosoftUsers](v.Value, v.Error)
		return
	},
	"microsoft.groups": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).Groups, ok = plugin.RawToTValue[*mqlMicrosoftGroups](v.Value, v.Error)
		return
	},
	"microsoft.domains": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).Domains, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.applications": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).Applications, ok = plugin.RawToTValue[*mqlMicrosoftApplications](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipals": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).Serviceprincipals, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.enterpriseApplications": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).EnterpriseApplications, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.roles": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).Roles, ok = plugin.RawToTValue[*mqlMicrosoftRoles](v.Value, v.Error)
		return
	},
	"microsoft.settings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).Settings, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.tenantDomainName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).TenantDomainName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoft).IdentityAndAccess, ok = plugin.RawToTValue[*mqlMicrosoftIdentityAndAccess](v.Value, v.Error)
		return
	},
	"microsoft.groups.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftGroups).__id, ok = v.Value.(string)
			return
		},
	"microsoft.groups.length": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroups).Length, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.groups.list": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroups).List, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.applications.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftApplications).__id, ok = v.Value.(string)
			return
		},
	"microsoft.applications.length": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplications).Length, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.applications.list": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplications).List, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.tenant.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftTenant).__id, ok = v.Value.(string)
			return
		},
	"microsoft.tenant.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.tenant.assignedPlans": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).AssignedPlans, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.tenant.provisionedPlans": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).ProvisionedPlans, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.tenant.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.tenant.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.tenant.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.tenant.verifiedDomains": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).VerifiedDomains, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.tenant.onPremisesSyncEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).OnPremisesSyncEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.tenant.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.tenant.subscriptions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).Subscriptions, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.tenant.settings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).Settings, ok = plugin.RawToTValue[*mqlMicrosoftTenantSettings](v.Value, v.Error)
		return
	},
	"microsoft.tenant.formsSettings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).FormsSettings, ok = plugin.RawToTValue[*mqlMicrosoftTenantFormsSettings](v.Value, v.Error)
		return
	},
	"microsoft.tenant.privacyProfile": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).PrivacyProfile, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.tenant.technicalNotificationMails": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).TechnicalNotificationMails, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.tenant.preferredLanguage": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenant).PreferredLanguage, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.tenant.settings.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftTenantSettings).__id, ok = v.Value.(string)
			return
		},
	"microsoft.tenant.settings.isAppAndServicesTrialEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantSettings).IsAppAndServicesTrialEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.settings.isOfficeStoreEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantSettings).IsOfficeStoreEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.formsSettings.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftTenantFormsSettings).__id, ok = v.Value.(string)
			return
		},
	"microsoft.tenant.formsSettings.isExternalSendFormEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantFormsSettings).IsExternalSendFormEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.formsSettings.isExternalShareCollaborationEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantFormsSettings).IsExternalShareCollaborationEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.formsSettings.isExternalShareResultEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantFormsSettings).IsExternalShareResultEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.formsSettings.isExternalShareTemplateEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantFormsSettings).IsExternalShareTemplateEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.formsSettings.isRecordIdentityByDefaultEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantFormsSettings).IsRecordIdentityByDefaultEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.formsSettings.isBingImageSearchEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantFormsSettings).IsBingImageSearchEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.tenant.formsSettings.isInOrgFormsPhishingScanEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftTenantFormsSettings).IsInOrgFormsPhishingScanEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.users.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUsers).__id, ok = v.Value.(string)
			return
		},
	"microsoft.users.filter": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUsers).Filter, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.users.search": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUsers).Search, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.users.list": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUsers).List, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftIdentityAndAccess).__id, ok = v.Value.(string)
			return
		},
	"microsoft.identityAndAccess.filter": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccess).Filter, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstances": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccess).RoleEligibilityScheduleInstances, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.list": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccess).List, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).__id, ok = v.Value.(string)
			return
		},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.principalId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).PrincipalId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.roleDefinitionId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).RoleDefinitionId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.directoryScopeId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).DirectoryScopeId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.appScopeId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).AppScopeId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.startDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).StartDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.endDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).EndDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.memberType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).MemberType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.roleEligibilityScheduleInstance.roleEligibilityScheduleId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance).RoleEligibilityScheduleId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftIdentityAndAccessPolicy).__id, ok = v.Value.(string)
			return
		},
	"microsoft.identityAndAccess.policy.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.isOrganizationDefault": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).IsOrganizationDefault, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.scopeId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).ScopeId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.scopeType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).ScopeType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.lastModifiedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).LastModifiedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.lastModifiedBy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).LastModifiedBy, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.rules": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicy).Rules, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.rule.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftIdentityAndAccessPolicyRule).__id, ok = v.Value.(string)
			return
		},
	"microsoft.identityAndAccess.policy.rule.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicyRule).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.rule.target": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicyRule).Target, ok = plugin.RawToTValue[*mqlMicrosoftIdentityAndAccessPolicyRuleTarget](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.rule.target.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).__id, ok = v.Value.(string)
			return
		},
	"microsoft.identityAndAccess.policy.rule.target.caller": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).Caller, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.rule.target.enforcedSettings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).EnforcedSettings, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.rule.target.inheritableSettings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).InheritableSettings, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.rule.target.level": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).Level, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.identityAndAccess.policy.rule.target.operations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftIdentityAndAccessPolicyRuleTarget).Operations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.assignedLicense.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserAssignedLicense).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.assignedLicense.disabledPlans": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAssignedLicense).DisabledPlans, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.assignedLicense.skuId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAssignedLicense).SkuId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetail.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserLicenseDetail).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.licenseDetail.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserLicenseDetail).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetail.skuId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserLicenseDetail).SkuId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetail.skuPartNumber": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserLicenseDetail).SkuPartNumber, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetail.servicePlans": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserLicenseDetail).ServicePlans, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetail.servicePlanInfo.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.licenseDetail.servicePlanInfo.appliesTo": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).AppliesTo, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetail.servicePlanInfo.provisioningStatus": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).ProvisioningStatus, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetail.servicePlanInfo.servicePlanId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).ServicePlanId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetail.servicePlanInfo.servicePlanName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserLicenseDetailServicePlanInfo).ServicePlanName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccess).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.namedLocations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccess).NamedLocations, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessNamedLocations](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policies": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccess).Policies, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccess).AuthenticationMethodsPolicy, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.lastModifiedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).LastModifiedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.policyVersion": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).PolicyVersion, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodsPolicy.authenticationMethodConfigurations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy).AuthenticationMethodConfigurations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodConfiguration.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.authenticationMethodConfiguration.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.authenticationMethodConfiguration.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.namedLocations.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessNamedLocations).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.namedLocations.ipLocations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessNamedLocations).IpLocations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.namedLocations.countryLocations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessNamedLocations).CountryLocations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicy).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.modifiedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).ModifiedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).Conditions, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyConditions](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).GrantControls, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyGrantControls](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).SessionControls, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicySessionControls](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.templateId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicy).TemplateId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyConditions).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.conditions.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.applications": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).Applications, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyConditionsApplications](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.authenticationFlows": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).AuthenticationFlows, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.clientApplications": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).ClientApplications, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyConditionsClientApplications](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.clientAppTypes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).ClientAppTypes, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.locations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).Locations, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyConditionsLocations](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.platforms": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).Platforms, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyConditionsPlatforms](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.servicePrincipalRiskLevels": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).ServicePrincipalRiskLevels, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.signInRiskLevels": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).SignInRiskLevels, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.userRiskLevels": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).UserRiskLevels, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.users": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).Users, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyConditionsUsers](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.insiderRiskLevels": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditions).InsiderRiskLevels, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.authenticationFlows.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.conditions.authenticationFlows.transferMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows).TransferMethods, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.allowedCombinations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).AllowedCombinations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.policyType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).PolicyType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.requirementsSatisfied": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).RequirementsSatisfied, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength.modifiedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength).ModifiedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency.authenticationType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency).AuthenticationType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency.frequencyInterval": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency).FrequencyInterval, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency.isEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency).IsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity.cloudAppSecurityType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity).CloudAppSecurityType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity.isEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity).IsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.persistentBrowser.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.sessionControls.persistentBrowser.mode": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser).Mode, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.persistentBrowser.isEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser).IsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions.isEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions).IsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.clientApplications.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyConditionsClientApplications).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.conditions.clientApplications.excludeServicePrincipals": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsClientApplications).ExcludeServicePrincipals, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.clientApplications.includeServicePrincipals": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsClientApplications).IncludeServicePrincipals, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.platforms.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyConditionsPlatforms).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.conditions.platforms.excludePlatforms": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsPlatforms).ExcludePlatforms, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.platforms.includePlatforms": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsPlatforms).IncludePlatforms, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.applications.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyConditionsApplications).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.conditions.applications.includeApplications": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsApplications).IncludeApplications, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.applications.excludeApplications": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsApplications).ExcludeApplications, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.applications.includeUserActions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsApplications).IncludeUserActions, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.users.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.conditions.users.includeUsers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).IncludeUsers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.users.excludeUsers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).ExcludeUsers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.users.includeGroups": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).IncludeGroups, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.users.excludeGroups": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).ExcludeGroups, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.users.includeRoles": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).IncludeRoles, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.users.excludeRoles": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsUsers).ExcludeRoles, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.locations.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyConditionsLocations).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.conditions.locations.includeLocations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsLocations).IncludeLocations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.conditions.locations.excludeLocations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyConditionsLocations).ExcludeLocations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.grantControls.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.operator": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).Operator, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.builtInControls": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).BuiltInControls, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.authenticationStrength": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).AuthenticationStrength, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.customAuthenticationFactors": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).CustomAuthenticationFactors, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.grantControls.termsOfUse": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicyGrantControls).TermsOfUse, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessPolicySessionControls).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.policy.sessionControls.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControls).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.signInFrequency": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControls).SignInFrequency, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControls).CloudAppSecurity, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.persistentBrowser": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControls).PersistentBrowser, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControls).ApplicationEnforcedRestrictions, ok = plugin.RawToTValue[*mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.policy.sessionControls.secureSignInSession": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessPolicySessionControls).SecureSignInSession, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.ipNamedLocation.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessIpNamedLocation).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.ipNamedLocation.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessIpNamedLocation).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.ipNamedLocation.trusted": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessIpNamedLocation).Trusted, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.countryNamedLocation.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftConditionalAccessCountryNamedLocation).__id, ok = v.Value.(string)
			return
		},
	"microsoft.conditionalAccess.countryNamedLocation.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessCountryNamedLocation).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.conditionalAccess.countryNamedLocation.lookupMethod": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftConditionalAccessCountryNamedLocation).LookupMethod, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUser).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.accountEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).AccountEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.city": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).City, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.companyName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).CompanyName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.country": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Country, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.user.department": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Department, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.employeeId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).EmployeeId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.givenName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).GivenName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.jobTitle": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).JobTitle, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.mail": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Mail, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.mobilePhone": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).MobilePhone, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.otherMails": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).OtherMails, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.officeLocation": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).OfficeLocation, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.postalCode": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).PostalCode, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.streetAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).StreetAddress, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.surname": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Surname, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.userPrincipalName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).UserPrincipalName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.userType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).UserType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.settings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Settings, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.job": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Job, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.contact": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Contact, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).AuthMethods, ok = plugin.RawToTValue[*mqlMicrosoftUserAuthenticationMethods](v.Value, v.Error)
		return
	},
	"microsoft.user.mfaEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).MfaEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.creationType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).CreationType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.identities": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Identities, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.auditlog": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).Auditlog, ok = plugin.RawToTValue[*mqlMicrosoftUserAuditlog](v.Value, v.Error)
		return
	},
	"microsoft.user.assignedLicenses": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).AssignedLicenses, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.licenseDetails": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).LicenseDetails, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationRequirements": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUser).AuthenticationRequirements, ok = plugin.RawToTValue[*mqlMicrosoftUserAuthenticationRequirements](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationRequirements.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserAuthenticationRequirements).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.authenticationRequirements.perUserMfaState": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationRequirements).PerUserMfaState, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.auditlog.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserAuditlog).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.auditlog.userId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuditlog).UserId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.auditlog.signins": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuditlog).Signins, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.auditlog.lastInteractiveSignIn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuditlog).LastInteractiveSignIn, ok = plugin.RawToTValue[*mqlMicrosoftUserSignin](v.Value, v.Error)
		return
	},
	"microsoft.user.auditlog.lastNonInteractiveSignIn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuditlog).LastNonInteractiveSignIn, ok = plugin.RawToTValue[*mqlMicrosoftUserSignin](v.Value, v.Error)
		return
	},
	"microsoft.user.identity.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserIdentity).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.identity.issuerAssignedId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserIdentity).IssuerAssignedId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.identity.issuer": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserIdentity).Issuer, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.identity.signInType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserIdentity).SignInType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.signin.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserSignin).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.signin.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserSignin).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.signin.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserSignin).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.user.signin.userId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserSignin).UserId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.signin.userDisplayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserSignin).UserDisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.signin.clientAppUsed": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserSignin).ClientAppUsed, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.signin.appDisplayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserSignin).AppDisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.signin.resourceDisplayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserSignin).ResourceDisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.signin.interactive": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserSignin).Interactive, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserAuthenticationMethods).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.authenticationMethods.count": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).Count, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.phoneMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).PhoneMethods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.emailMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).EmailMethods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.fido2Methods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).Fido2Methods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.softwareMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).SoftwareMethods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.microsoftAuthenticator": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).MicrosoftAuthenticator, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.passwordMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).PasswordMethods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.temporaryAccessPassMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).TemporaryAccessPassMethods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.windowsHelloMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).WindowsHelloMethods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.registrationDetails": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethods).RegistrationDetails, ok = plugin.RawToTValue[*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).__id, ok = v.Value.(string)
			return
		},
	"microsoft.user.authenticationMethods.userRegistrationDetails.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isAdmin": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).IsAdmin, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isMfaCapable": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).IsMfaCapable, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isMfaRegistered": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).IsMfaRegistered, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isPasswordlessCapable": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).IsPasswordlessCapable, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isSsprCapable": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).IsSsprCapable, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isSsprEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).IsSsprEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isSsprRegistered": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).IsSsprRegistered, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.isSystemPreferredAuthenticationMethodEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).IsSystemPreferredAuthenticationMethodEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.lastUpdatedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).LastUpdatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.methodsRegistered": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).MethodsRegistered, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.systemPreferredAuthenticationMethods": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).SystemPreferredAuthenticationMethods, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.userDisplayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).UserDisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.userPreferredMethodForSecondaryAuthentication": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).UserPreferredMethodForSecondaryAuthentication, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.userPrincipalName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).UserPrincipalName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.user.authenticationMethods.userRegistrationDetails.userType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails).UserType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.group.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftGroup).__id, ok = v.Value.(string)
			return
		},
	"microsoft.group.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.group.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.group.securityEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).SecurityEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.group.mailEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).MailEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.group.mailNickname": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).MailNickname, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.group.mail": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).Mail, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.group.visibility": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).Visibility, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.group.members": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).Members, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.group.groupTypes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).GroupTypes, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.group.membershipRule": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).MembershipRule, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.group.membershipRuleProcessingState": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGroup).MembershipRuleProcessingState, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devices.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDevices).__id, ok = v.Value.(string)
			return
		},
	"microsoft.devices.filter": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevices).Filter, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devices.search": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevices).Search, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devices.list": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevices).List, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.device.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDevice).__id, ok = v.Value.(string)
			return
		},
	"microsoft.device.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.deviceId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).DeviceId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.deviceCategory": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).DeviceCategory, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.enrollmentProfileName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).EnrollmentProfileName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.enrollmentType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).EnrollmentType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.isCompliant": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).IsCompliant, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.device.isManaged": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).IsManaged, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.device.manufacturer": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).Manufacturer, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.isRooted": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).IsRooted, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.device.mdmAppId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).MdmAppId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.model": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).Model, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.operatingSystem": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).OperatingSystem, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.operatingSystemVersion": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).OperatingSystemVersion, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.device.physicalIds": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).PhysicalIds, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.device.registrationDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).RegistrationDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.device.systemLabels": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).SystemLabels, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.device.trustType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevice).TrustType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.domain.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDomain).__id, ok = v.Value.(string)
			return
		},
	"microsoft.domain.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.domain.authenticationType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).AuthenticationType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.domain.availabilityStatus": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).AvailabilityStatus, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.domain.isAdminManaged": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).IsAdminManaged, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.domain.isDefault": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).IsDefault, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.domain.isInitial": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).IsInitial, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.domain.isRoot": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).IsRoot, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.domain.isVerified": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).IsVerified, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.domain.passwordNotificationWindowInDays": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).PasswordNotificationWindowInDays, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.domain.passwordValidityPeriodInDays": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).PasswordValidityPeriodInDays, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.domain.supportedServices": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).SupportedServices, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.domain.serviceConfigurationRecords": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomain).ServiceConfigurationRecords, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.domaindnsrecord.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDomaindnsrecord).__id, ok = v.Value.(string)
			return
		},
	"microsoft.domaindnsrecord.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomaindnsrecord).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.domaindnsrecord.isOptional": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomaindnsrecord).IsOptional, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.domaindnsrecord.label": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomaindnsrecord).Label, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.domaindnsrecord.recordType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomaindnsrecord).RecordType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.domaindnsrecord.supportedService": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomaindnsrecord).SupportedService, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.domaindnsrecord.ttl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomaindnsrecord).Ttl, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.domaindnsrecord.properties": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDomaindnsrecord).Properties, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftApplication).__id, ok = v.Value.(string)
			return
		},
	"microsoft.application.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.appId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).AppId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.notes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Notes, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.tags": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Tags, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.applicationTemplateId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).ApplicationTemplateId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.disabledByMicrosoftStatus": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).DisabledByMicrosoftStatus, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.groupMembershipClaims": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).GroupMembershipClaims, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.createdAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).CreatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.application.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.application.identifierUris": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).IdentifierUris, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.publisherDomain": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).PublisherDomain, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.signInAudience": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).SignInAudience, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.info": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Info, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.api": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Api, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.web": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Web, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.spa": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Spa, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.secrets": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Secrets, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.certificates": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Certificates, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.hasExpiredCredentials": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).HasExpiredCredentials, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.application.owners": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Owners, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.servicePrincipal": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).ServicePrincipal, ok = plugin.RawToTValue[*mqlMicrosoftServiceprincipal](v.Value, v.Error)
		return
	},
	"microsoft.application.isDeviceOnlyAuthSupported": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).IsDeviceOnlyAuthSupported, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.application.isFallbackPublicClient": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).IsFallbackPublicClient, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.application.nativeAuthenticationApisEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).NativeAuthenticationApisEnabled, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.serviceManagementReference": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).ServiceManagementReference, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.tokenEncryptionKeyId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).TokenEncryptionKeyId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.samlMetadataUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).SamlMetadataUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.defaultRedirectUri": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).DefaultRedirectUri, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.certification": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).Certification, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.optionalClaims": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).OptionalClaims, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.servicePrincipalLockConfiguration": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).ServicePrincipalLockConfiguration, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.requestSignatureVerification": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).RequestSignatureVerification, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.parentalControlSettings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).ParentalControlSettings, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.publicClient": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).PublicClient, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.appRoles": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplication).AppRoles, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.role.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftApplicationRole).__id, ok = v.Value.(string)
			return
		},
	"microsoft.application.role.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationRole).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.role.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationRole).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.role.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationRole).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.role.value": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationRole).Value, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.role.allowedMemberTypes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationRole).AllowedMemberTypes, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.application.role.isEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationRole).IsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.keyCredential.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftKeyCredential).__id, ok = v.Value.(string)
			return
		},
	"microsoft.keyCredential.keyId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftKeyCredential).KeyId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.keyCredential.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftKeyCredential).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.keyCredential.thumbprint": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftKeyCredential).Thumbprint, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.keyCredential.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftKeyCredential).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.keyCredential.usage": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftKeyCredential).Usage, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.keyCredential.expires": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftKeyCredential).Expires, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.keyCredential.expired": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftKeyCredential).Expired, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.passwordCredential.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftPasswordCredential).__id, ok = v.Value.(string)
			return
		},
	"microsoft.passwordCredential.keyId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPasswordCredential).KeyId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.passwordCredential.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPasswordCredential).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.passwordCredential.hint": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPasswordCredential).Hint, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.passwordCredential.expires": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPasswordCredential).Expires, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.passwordCredential.expired": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPasswordCredential).Expired, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftServiceprincipal).__id, ok = v.Value.(string)
			return
		},
	"microsoft.serviceprincipal.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.appId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).AppId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.appOwnerOrganizationId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).AppOwnerOrganizationId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.tags": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Tags, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.enabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Enabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.homepageUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).HomepageUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.termsOfServiceUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).TermsOfServiceUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.replyUrls": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).ReplyUrls, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.assignmentRequired": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).AssignmentRequired, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.visibleToUsers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).VisibleToUsers, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.notes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Notes, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.assignments": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Assignments, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.applicationTemplateId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).ApplicationTemplateId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.verifiedPublisher": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).VerifiedPublisher, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.loginUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).LoginUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.logoutUrl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).LogoutUrl, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.servicePrincipalNames": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).ServicePrincipalNames, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.signInAudience": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).SignInAudience, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.preferredSingleSignOnMode": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).PreferredSingleSignOnMode, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.notificationEmailAddresses": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).NotificationEmailAddresses, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.appRoleAssignmentRequired": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).AppRoleAssignmentRequired, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.accountEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).AccountEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.isFirstParty": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).IsFirstParty, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.appRoles": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).AppRoles, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.permissions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipal).Permissions, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.assignment.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftServiceprincipalAssignment).__id, ok = v.Value.(string)
			return
		},
	"microsoft.serviceprincipal.assignment.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipalAssignment).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.assignment.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipalAssignment).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.serviceprincipal.assignment.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftServiceprincipalAssignment).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.permission.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftApplicationPermission).__id, ok = v.Value.(string)
			return
		},
	"microsoft.application.permission.appId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationPermission).AppId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.permission.appName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationPermission).AppName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.permission.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationPermission).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.permission.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationPermission).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.permission.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationPermission).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.permission.type": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationPermission).Type, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.application.permission.status": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftApplicationPermission).Status, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftSecurity).__id, ok = v.Value.(string)
			return
		},
	"microsoft.security.secureScores": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurity).SecureScores, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.security.latestSecureScores": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurity).LatestSecureScores, ok = plugin.RawToTValue[*mqlMicrosoftSecuritySecurityscore](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUsers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurity).RiskyUsers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftSecuritySecurityscore).__id, ok = v.Value.(string)
			return
		},
	"microsoft.security.securityscore.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.activeUserCount": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).ActiveUserCount, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.averageComparativeScores": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).AverageComparativeScores, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.azureTenantId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).AzureTenantId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.controlScores": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).ControlScores, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.currentScore": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).CurrentScore, ok = plugin.RawToTValue[float64](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.enabledServices": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).EnabledServices, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.licensedUserCount": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).LicensedUserCount, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.maxScore": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).MaxScore, ok = plugin.RawToTValue[float64](v.Value, v.Error)
		return
	},
	"microsoft.security.securityscore.vendorInformation": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecuritySecurityscore).VendorInformation, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUser.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftSecurityRiskyUser).__id, ok = v.Value.(string)
			return
		},
	"microsoft.security.riskyUser.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurityRiskyUser).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUser.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurityRiskyUser).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUser.principalName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurityRiskyUser).PrincipalName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUser.user": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurityRiskyUser).User, ok = plugin.RawToTValue[*mqlMicrosoftUser](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUser.riskDetail": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurityRiskyUser).RiskDetail, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUser.riskLevel": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurityRiskyUser).RiskLevel, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUser.riskState": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurityRiskyUser).RiskState, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.security.riskyUser.lastUpdatedAt": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftSecurityRiskyUser).LastUpdatedAt, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.policies.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftPolicies).__id, ok = v.Value.(string)
			return
		},
	"microsoft.policies.authorizationPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPolicies).AuthorizationPolicy, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.policies.identitySecurityDefaultsEnforcementPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPolicies).IdentitySecurityDefaultsEnforcementPolicy, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.policies.adminConsentRequestPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPolicies).AdminConsentRequestPolicy, ok = plugin.RawToTValue[*mqlMicrosoftAdminConsentRequestPolicy](v.Value, v.Error)
		return
	},
	"microsoft.policies.permissionGrantPolicies": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPolicies).PermissionGrantPolicies, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.policies.consentPolicySettings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPolicies).ConsentPolicySettings, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodsPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPolicies).AuthenticationMethodsPolicy, ok = plugin.RawToTValue[*mqlMicrosoftPoliciesAuthenticationMethodsPolicy](v.Value, v.Error)
		return
	},
	"microsoft.adminConsentRequestPolicy.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftAdminConsentRequestPolicy).__id, ok = v.Value.(string)
			return
		},
	"microsoft.adminConsentRequestPolicy.isEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftAdminConsentRequestPolicy).IsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.adminConsentRequestPolicy.notifyReviewers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftAdminConsentRequestPolicy).NotifyReviewers, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.adminConsentRequestPolicy.remindersEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftAdminConsentRequestPolicy).RemindersEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.adminConsentRequestPolicy.requestDurationInDays": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftAdminConsentRequestPolicy).RequestDurationInDays, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.adminConsentRequestPolicy.reviewers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftAdminConsentRequestPolicy).Reviewers, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.adminConsentRequestPolicy.version": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftAdminConsentRequestPolicy).Version, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.graph.accessReviewReviewerScope.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftGraphAccessReviewReviewerScope).__id, ok = v.Value.(string)
			return
		},
	"microsoft.graph.accessReviewReviewerScope.query": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGraphAccessReviewReviewerScope).Query, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.graph.accessReviewReviewerScope.queryRoot": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGraphAccessReviewReviewerScope).QueryRoot, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.graph.accessReviewReviewerScope.queryType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftGraphAccessReviewReviewerScope).QueryType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodsPolicy.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).__id, ok = v.Value.(string)
			return
		},
	"microsoft.policies.authenticationMethodsPolicy.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodsPolicy.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodsPolicy.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodsPolicy.lastModifiedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).LastModifiedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodsPolicy.policyVersion": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).PolicyVersion, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodsPolicy.authenticationMethodConfigurations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy).AuthenticationMethodConfigurations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodConfiguration.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftPoliciesAuthenticationMethodConfiguration).__id, ok = v.Value.(string)
			return
		},
	"microsoft.policies.authenticationMethodConfiguration.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodConfiguration).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodConfiguration.state": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodConfiguration).State, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.policies.authenticationMethodConfiguration.excludeTargets": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftPoliciesAuthenticationMethodConfiguration).ExcludeTargets, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.roles.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftRoles).__id, ok = v.Value.(string)
			return
		},
	"microsoft.roles.filter": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRoles).Filter, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.roles.search": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRoles).Search, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.roles.list": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRoles).List, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftRolemanagement).__id, ok = v.Value.(string)
			return
		},
	"microsoft.rolemanagement.roleDefinitions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagement).RoleDefinitions, ok = plugin.RawToTValue[*mqlMicrosoftRoles](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftRolemanagementRoledefinition).__id, ok = v.Value.(string)
			return
		},
	"microsoft.rolemanagement.roledefinition.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.isBuiltIn": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).IsBuiltIn, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.isEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).IsEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.rolePermissions": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).RolePermissions, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.templateId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).TemplateId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.version": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).Version, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roledefinition.assignments": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoledefinition).Assignments, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roleassignment.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftRolemanagementRoleassignment).__id, ok = v.Value.(string)
			return
		},
	"microsoft.rolemanagement.roleassignment.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoleassignment).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roleassignment.roleDefinitionId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoleassignment).RoleDefinitionId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roleassignment.principalId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoleassignment).PrincipalId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.rolemanagement.roleassignment.principal": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftRolemanagementRoleassignment).Principal, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDevicemanagement).__id, ok = v.Value.(string)
			return
		},
	"microsoft.devicemanagement.managedDevices": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagement).ManagedDevices, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceConfigurations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagement).DeviceConfigurations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceCompliancePolicies": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagement).DeviceCompliancePolicies, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceEnrollmentConfigurations": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagement).DeviceEnrollmentConfigurations, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.settings": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagement).Settings, ok = plugin.RawToTValue[*mqlMicrosoftDevicemanagementSettings](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).__id, ok = v.Value.(string)
			return
		},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.priority": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).Priority, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.lastModifiedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).LastModifiedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceEnrollmentConfiguration.version": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration).Version, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDevicemanagementManageddevice).__id, ok = v.Value.(string)
			return
		},
	"microsoft.devicemanagement.manageddevice.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.userId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).UserId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.name": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Name, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.operatingSystem": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).OperatingSystem, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.jailBroken": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).JailBroken, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.osVersion": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).OsVersion, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.easActivated": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).EasActivated, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.easDeviceId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).EasDeviceId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.azureADRegistered": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).AzureADRegistered, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.emailAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).EmailAddress, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.azureActiveDirectoryDeviceId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).AzureActiveDirectoryDeviceId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.deviceCategoryDisplayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).DeviceCategoryDisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.isSupervised": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).IsSupervised, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.isEncrypted": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).IsEncrypted, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.userPrincipalName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).UserPrincipalName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.model": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Model, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.manufacturer": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Manufacturer, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.imei": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Imei, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.serialNumber": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).SerialNumber, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.androidSecurityPatchLevel": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).AndroidSecurityPatchLevel, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.userDisplayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).UserDisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.wiFiMacAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).WiFiMacAddress, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.meid": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Meid, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.iccid": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Iccid, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.udid": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Udid, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.notes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).Notes, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.ethernetMacAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).EthernetMacAddress, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.enrollmentProfileName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).EnrollmentProfileName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.manageddevice.windowsProtectionState": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementManageddevice).WindowsProtectionState, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceconfiguration.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).__id, ok = v.Value.(string)
			return
		},
	"microsoft.devicemanagement.deviceconfiguration.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceconfiguration.lastModifiedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).LastModifiedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceconfiguration.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceconfiguration.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceconfiguration.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceconfiguration.version": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).Version, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.deviceconfiguration.properties": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDeviceconfiguration).Properties, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.devicecompliancepolicy.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).__id, ok = v.Value.(string)
			return
		},
	"microsoft.devicemanagement.devicecompliancepolicy.id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).Id, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.devicecompliancepolicy.createdDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).CreatedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.devicecompliancepolicy.description": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).Description, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.devicecompliancepolicy.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.devicecompliancepolicy.lastModifiedDateTime": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).LastModifiedDateTime, ok = plugin.RawToTValue[*time.Time](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.devicecompliancepolicy.version": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).Version, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.devicecompliancepolicy.assignments": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).Assignments, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.devicecompliancepolicy.properties": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementDevicecompliancepolicy).Properties, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.settings.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMicrosoftDevicemanagementSettings).__id, ok = v.Value.(string)
			return
		},
	"microsoft.devicemanagement.settings.secureByDefault": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementSettings).SecureByDefault, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.settings.isScheduledActionEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementSettings).IsScheduledActionEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"microsoft.devicemanagement.settings.deviceComplianceCheckinThresholdDays": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMicrosoftDevicemanagementSettings).DeviceComplianceCheckinThresholdDays, ok = plugin.RawToTValue[int64](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365Exchangeonline).__id, ok = v.Value.(string)
			return
		},
	"ms365.exchangeonline.malwareFilterPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).MalwareFilterPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.hostedOutboundSpamFilterPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).HostedOutboundSpamFilterPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.transportRule": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).TransportRule, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.remoteDomain": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).RemoteDomain, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.safeLinksPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).SafeLinksPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.safeAttachmentPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).SafeAttachmentPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.organizationConfig": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).OrganizationConfig, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.authenticationPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).AuthenticationPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.antiPhishPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).AntiPhishPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.dkimSigningConfig": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).DkimSigningConfig, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.owaMailboxPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).OwaMailboxPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.adminAuditLogConfig": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).AdminAuditLogConfig, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.phishFilterPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).PhishFilterPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).Mailbox, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.atpPolicyForO365": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).AtpPolicyForO365, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.sharingPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).SharingPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.roleAssignmentPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).RoleAssignmentPolicy, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.externalInOutlook": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).ExternalInOutlook, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.sharedMailboxes": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).SharedMailboxes, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.teamsProtectionPolicies": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).TeamsProtectionPolicies, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicies": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).ReportSubmissionPolicies, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailboxesWithAudit": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).MailboxesWithAudit, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.transportConfig": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).TransportConfig, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.securityAndCompliance": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Exchangeonline).SecurityAndCompliance, ok = plugin.RawToTValue[*mqlMs365ExchangeonlineSecurityAndCompliance](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.securityAndCompliance.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365ExchangeonlineSecurityAndCompliance).__id, ok = v.Value.(string)
			return
		},
	"ms365.exchangeonline.securityAndCompliance.dlpCompliancePolicies": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineSecurityAndCompliance).DlpCompliancePolicies, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.teamsProtectionPolicy.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365ExchangeonlineTeamsProtectionPolicy).__id, ok = v.Value.(string)
			return
		},
	"ms365.exchangeonline.teamsProtectionPolicy.zapEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineTeamsProtectionPolicy).ZapEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.teamsProtectionPolicy.isValid": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineTeamsProtectionPolicy).IsValid, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicy.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).__id, ok = v.Value.(string)
			return
		},
	"ms365.exchangeonline.reportSubmissionPolicy.reportJunkToCustomizedAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).ReportJunkToCustomizedAddress, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportNotJunkToCustomizedAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).ReportNotJunkToCustomizedAddress, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportPhishToCustomizedAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).ReportPhishToCustomizedAddress, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportJunkAddresses": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).ReportJunkAddresses, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportNotJunkAddresses": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).ReportNotJunkAddresses, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportPhishAddresses": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).ReportPhishAddresses, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportChatMessageEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).ReportChatMessageEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.reportSubmissionPolicy.reportChatMessageToCustomizedAddressEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineReportSubmissionPolicy).ReportChatMessageToCustomizedAddressEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.externalSender.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365ExchangeonlineExternalSender).__id, ok = v.Value.(string)
			return
		},
	"ms365.exchangeonline.externalSender.identity": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineExternalSender).Identity, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.externalSender.allowList": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineExternalSender).AllowList, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.externalSender.enabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineExternalSender).Enabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.exoMailbox.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365ExchangeonlineExoMailbox).__id, ok = v.Value.(string)
			return
		},
	"ms365.exchangeonline.exoMailbox.identity": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineExoMailbox).Identity, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.exoMailbox.user": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineExoMailbox).User, ok = plugin.RawToTValue[*mqlMicrosoftUser](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.exoMailbox.externalDirectoryObjectId": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineExoMailbox).ExternalDirectoryObjectId, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365ExchangeonlineMailbox).__id, ok = v.Value.(string)
			return
		},
	"ms365.exchangeonline.mailbox.identity": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).Identity, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.displayName": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).DisplayName, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.primarySmtpAddress": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).PrimarySmtpAddress, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.recipientTypeDetails": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).RecipientTypeDetails, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.auditEnabled": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).AuditEnabled, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.auditAdmin": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).AuditAdmin, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.auditDelegate": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).AuditDelegate, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.auditOwner": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).AuditOwner, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.exchangeonline.mailbox.auditLogAgeLimit": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365ExchangeonlineMailbox).AuditLogAgeLimit, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.sharepointonline.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365Sharepointonline).__id, ok = v.Value.(string)
			return
		},
	"ms365.sharepointonline.spoTenant": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Sharepointonline).SpoTenant, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"ms365.sharepointonline.spoTenantSyncClientRestriction": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Sharepointonline).SpoTenantSyncClientRestriction, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"ms365.sharepointonline.spoSites": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Sharepointonline).SpoSites, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.sharepointonline.defaultLinkPermission": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Sharepointonline).DefaultLinkPermission, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.sharepointonline.site.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365SharepointonlineSite).__id, ok = v.Value.(string)
			return
		},
	"ms365.sharepointonline.site.url": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365SharepointonlineSite).Url, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.sharepointonline.site.denyAddAndCustomizePages": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365SharepointonlineSite).DenyAddAndCustomizePages, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365Teams).__id, ok = v.Value.(string)
			return
		},
	"ms365.teams.csTeamsClientConfiguration": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Teams).CsTeamsClientConfiguration, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"ms365.teams.csTenantFederationConfiguration": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Teams).CsTenantFederationConfiguration, ok = plugin.RawToTValue[*mqlMs365TeamsTenantFederationConfig](v.Value, v.Error)
		return
	},
	"ms365.teams.csTeamsMeetingPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Teams).CsTeamsMeetingPolicy, ok = plugin.RawToTValue[*mqlMs365TeamsTeamsMeetingPolicyConfig](v.Value, v.Error)
		return
	},
	"ms365.teams.csTeamsMessagingPolicy": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365Teams).CsTeamsMessagingPolicy, ok = plugin.RawToTValue[*mqlMs365TeamsTeamsMessagingPolicyConfig](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365TeamsTenantFederationConfig).__id, ok = v.Value.(string)
			return
		},
	"ms365.teams.tenantFederationConfig.identity": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).Identity, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.blockedDomains": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).BlockedDomains, ok = plugin.RawToTValue[interface{}](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.allowedDomains": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).AllowedDomains, ok = plugin.RawToTValue[[]interface{}](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.allowFederatedUsers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).AllowFederatedUsers, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.allowPublicUsers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).AllowPublicUsers, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.allowTeamsConsumer": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).AllowTeamsConsumer, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.allowTeamsConsumerInbound": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).AllowTeamsConsumerInbound, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.treatDiscoveredPartnersAsUnverified": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).TreatDiscoveredPartnersAsUnverified, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.sharedSipAddressSpace": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).SharedSipAddressSpace, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.tenantFederationConfig.restrictTeamsConsumerToExternalUserProfiles": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTenantFederationConfig).RestrictTeamsConsumerToExternalUserProfiles, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).__id, ok = v.Value.(string)
			return
		},
	"ms365.teams.teamsMeetingPolicyConfig.allowAnonymousUsersToJoinMeeting": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).AllowAnonymousUsersToJoinMeeting, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowAnonymousUsersToStartMeeting": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).AllowAnonymousUsersToStartMeeting, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowExternalNonTrustedMeetingChat": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).AllowExternalNonTrustedMeetingChat, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.autoAdmittedUsers": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).AutoAdmittedUsers, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowPSTNUsersToBypassLobby": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).AllowPSTNUsersToBypassLobby, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.meetingChatEnabledType": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).MeetingChatEnabledType, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.designatedPresenterRoleMode": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).DesignatedPresenterRoleMode, ok = plugin.RawToTValue[string](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowExternalParticipantGiveRequestControl": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).AllowExternalParticipantGiveRequestControl, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowSecurityEndUserReporting": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).AllowSecurityEndUserReporting, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMeetingPolicyConfig.allowCloudRecordingForCalls": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMeetingPolicyConfig).AllowCloudRecordingForCalls, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
	"ms365.teams.teamsMessagingPolicyConfig.__id": func(r plugin.Resource, v *llx.RawData) (ok bool) {
			r.(*mqlMs365TeamsTeamsMessagingPolicyConfig).__id, ok = v.Value.(string)
			return
		},
	"ms365.teams.teamsMessagingPolicyConfig.allowSecurityEndUserReporting": func(r plugin.Resource, v *llx.RawData) (ok bool) {
		r.(*mqlMs365TeamsTeamsMessagingPolicyConfig).AllowSecurityEndUserReporting, ok = plugin.RawToTValue[bool](v.Value, v.Error)
		return
	},
}

func SetData(resource plugin.Resource, field string, val *llx.RawData) error {
	f, ok := setDataFields[resource.MqlName() + "." + field]
	if !ok {
		return errors.New("[ms365] cannot set '"+field+"' in resource '"+resource.MqlName()+"', field not found")
	}

	if ok := f(resource, val); !ok {
		return errors.New("[ms365] cannot set '"+field+"' in resource '"+resource.MqlName()+"', type does not match")
	}
	return nil
}

func SetAllData(resource plugin.Resource, args map[string]*llx.RawData) error {
	var err error
	for k, v := range args {
		if err = SetData(resource, k, v); err != nil {
			return err
		}
	}
	return nil
}

// mqlMicrosoft for the microsoft resource
type mqlMicrosoft struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlMicrosoftInternal
	Organizations plugin.TValue[[]interface{}]
	Users plugin.TValue[*mqlMicrosoftUsers]
	Groups plugin.TValue[*mqlMicrosoftGroups]
	Domains plugin.TValue[[]interface{}]
	Applications plugin.TValue[*mqlMicrosoftApplications]
	Serviceprincipals plugin.TValue[[]interface{}]
	EnterpriseApplications plugin.TValue[[]interface{}]
	Roles plugin.TValue[*mqlMicrosoftRoles]
	Settings plugin.TValue[interface{}]
	TenantDomainName plugin.TValue[string]
	IdentityAndAccess plugin.TValue[*mqlMicrosoftIdentityAndAccess]
}

// createMicrosoft creates a new instance of this resource
func createMicrosoft(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoft{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoft) MqlName() string {
	return "microsoft"
}

func (c *mqlMicrosoft) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoft) GetOrganizations() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Organizations, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "organizations")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.organizations()
	})
}

func (c *mqlMicrosoft) GetUsers() *plugin.TValue[*mqlMicrosoftUsers] {
	return plugin.GetOrCompute[*mqlMicrosoftUsers](&c.Users, func() (*mqlMicrosoftUsers, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "users")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUsers), nil
			}
		}

		return c.users()
	})
}

func (c *mqlMicrosoft) GetGroups() *plugin.TValue[*mqlMicrosoftGroups] {
	return plugin.GetOrCompute[*mqlMicrosoftGroups](&c.Groups, func() (*mqlMicrosoftGroups, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "groups")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftGroups), nil
			}
		}

		return c.groups()
	})
}

func (c *mqlMicrosoft) GetDomains() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Domains, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "domains")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.domains()
	})
}

func (c *mqlMicrosoft) GetApplications() *plugin.TValue[*mqlMicrosoftApplications] {
	return plugin.GetOrCompute[*mqlMicrosoftApplications](&c.Applications, func() (*mqlMicrosoftApplications, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "applications")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftApplications), nil
			}
		}

		return c.applications()
	})
}

func (c *mqlMicrosoft) GetServiceprincipals() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Serviceprincipals, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "serviceprincipals")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.serviceprincipals()
	})
}

func (c *mqlMicrosoft) GetEnterpriseApplications() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.EnterpriseApplications, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "enterpriseApplications")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.enterpriseApplications()
	})
}

func (c *mqlMicrosoft) GetRoles() *plugin.TValue[*mqlMicrosoftRoles] {
	return plugin.GetOrCompute[*mqlMicrosoftRoles](&c.Roles, func() (*mqlMicrosoftRoles, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "roles")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftRoles), nil
			}
		}

		return c.roles()
	})
}

func (c *mqlMicrosoft) GetSettings() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.Settings, func() (interface{}, error) {
		return c.settings()
	})
}

func (c *mqlMicrosoft) GetTenantDomainName() *plugin.TValue[string] {
	return plugin.GetOrCompute[string](&c.TenantDomainName, func() (string, error) {
		return c.tenantDomainName()
	})
}

func (c *mqlMicrosoft) GetIdentityAndAccess() *plugin.TValue[*mqlMicrosoftIdentityAndAccess] {
	return plugin.GetOrCompute[*mqlMicrosoftIdentityAndAccess](&c.IdentityAndAccess, func() (*mqlMicrosoftIdentityAndAccess, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft", c.__id, "identityAndAccess")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftIdentityAndAccess), nil
			}
		}

		return c.identityAndAccess()
	})
}

// mqlMicrosoftGroups for the microsoft.groups resource
type mqlMicrosoftGroups struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftGroupsInternal it will be used here
	Length plugin.TValue[int64]
	List plugin.TValue[[]interface{}]
}

// createMicrosoftGroups creates a new instance of this resource
func createMicrosoftGroups(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftGroups{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.groups", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftGroups) MqlName() string {
	return "microsoft.groups"
}

func (c *mqlMicrosoftGroups) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftGroups) GetLength() *plugin.TValue[int64] {
	return plugin.GetOrCompute[int64](&c.Length, func() (int64, error) {
		return c.length()
	})
}

func (c *mqlMicrosoftGroups) GetList() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.List, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.groups", c.__id, "list")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.list()
	})
}

// mqlMicrosoftApplications for the microsoft.applications resource
type mqlMicrosoftApplications struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftApplicationsInternal it will be used here
	Length plugin.TValue[int64]
	List plugin.TValue[[]interface{}]
}

// createMicrosoftApplications creates a new instance of this resource
func createMicrosoftApplications(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftApplications{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.applications", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftApplications) MqlName() string {
	return "microsoft.applications"
}

func (c *mqlMicrosoftApplications) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftApplications) GetLength() *plugin.TValue[int64] {
	return plugin.GetOrCompute[int64](&c.Length, func() (int64, error) {
		return c.length()
	})
}

func (c *mqlMicrosoftApplications) GetList() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.List, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.applications", c.__id, "list")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.list()
	})
}

// mqlMicrosoftTenant for the microsoft.tenant resource
type mqlMicrosoftTenant struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftTenantInternal it will be used here
	Id plugin.TValue[string]
	AssignedPlans plugin.TValue[[]interface{}]
	ProvisionedPlans plugin.TValue[[]interface{}]
	CreatedDateTime plugin.TValue[*time.Time]
	DisplayName plugin.TValue[string]
	Name plugin.TValue[string]
	VerifiedDomains plugin.TValue[[]interface{}]
	OnPremisesSyncEnabled plugin.TValue[bool]
	CreatedAt plugin.TValue[*time.Time]
	Type plugin.TValue[string]
	Subscriptions plugin.TValue[[]interface{}]
	Settings plugin.TValue[*mqlMicrosoftTenantSettings]
	FormsSettings plugin.TValue[*mqlMicrosoftTenantFormsSettings]
	PrivacyProfile plugin.TValue[interface{}]
	TechnicalNotificationMails plugin.TValue[[]interface{}]
	PreferredLanguage plugin.TValue[string]
}

// createMicrosoftTenant creates a new instance of this resource
func createMicrosoftTenant(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftTenant{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.tenant", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftTenant) MqlName() string {
	return "microsoft.tenant"
}

func (c *mqlMicrosoftTenant) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftTenant) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftTenant) GetAssignedPlans() *plugin.TValue[[]interface{}] {
	return &c.AssignedPlans
}

func (c *mqlMicrosoftTenant) GetProvisionedPlans() *plugin.TValue[[]interface{}] {
	return &c.ProvisionedPlans
}

func (c *mqlMicrosoftTenant) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftTenant) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftTenant) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftTenant) GetVerifiedDomains() *plugin.TValue[[]interface{}] {
	return &c.VerifiedDomains
}

func (c *mqlMicrosoftTenant) GetOnPremisesSyncEnabled() *plugin.TValue[bool] {
	return &c.OnPremisesSyncEnabled
}

func (c *mqlMicrosoftTenant) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlMicrosoftTenant) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlMicrosoftTenant) GetSubscriptions() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Subscriptions, func() ([]interface{}, error) {
		return c.subscriptions()
	})
}

func (c *mqlMicrosoftTenant) GetSettings() *plugin.TValue[*mqlMicrosoftTenantSettings] {
	return plugin.GetOrCompute[*mqlMicrosoftTenantSettings](&c.Settings, func() (*mqlMicrosoftTenantSettings, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.tenant", c.__id, "settings")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftTenantSettings), nil
			}
		}

		return c.settings()
	})
}

func (c *mqlMicrosoftTenant) GetFormsSettings() *plugin.TValue[*mqlMicrosoftTenantFormsSettings] {
	return plugin.GetOrCompute[*mqlMicrosoftTenantFormsSettings](&c.FormsSettings, func() (*mqlMicrosoftTenantFormsSettings, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.tenant", c.__id, "formsSettings")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftTenantFormsSettings), nil
			}
		}

		return c.formsSettings()
	})
}

func (c *mqlMicrosoftTenant) GetPrivacyProfile() *plugin.TValue[interface{}] {
	return &c.PrivacyProfile
}

func (c *mqlMicrosoftTenant) GetTechnicalNotificationMails() *plugin.TValue[[]interface{}] {
	return &c.TechnicalNotificationMails
}

func (c *mqlMicrosoftTenant) GetPreferredLanguage() *plugin.TValue[string] {
	return &c.PreferredLanguage
}

// mqlMicrosoftTenantSettings for the microsoft.tenant.settings resource
type mqlMicrosoftTenantSettings struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftTenantSettingsInternal it will be used here
	IsAppAndServicesTrialEnabled plugin.TValue[bool]
	IsOfficeStoreEnabled plugin.TValue[bool]
}

// createMicrosoftTenantSettings creates a new instance of this resource
func createMicrosoftTenantSettings(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftTenantSettings{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.tenant.settings", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftTenantSettings) MqlName() string {
	return "microsoft.tenant.settings"
}

func (c *mqlMicrosoftTenantSettings) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftTenantSettings) GetIsAppAndServicesTrialEnabled() *plugin.TValue[bool] {
	return &c.IsAppAndServicesTrialEnabled
}

func (c *mqlMicrosoftTenantSettings) GetIsOfficeStoreEnabled() *plugin.TValue[bool] {
	return &c.IsOfficeStoreEnabled
}

// mqlMicrosoftTenantFormsSettings for the microsoft.tenant.formsSettings resource
type mqlMicrosoftTenantFormsSettings struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftTenantFormsSettingsInternal it will be used here
	IsExternalSendFormEnabled plugin.TValue[bool]
	IsExternalShareCollaborationEnabled plugin.TValue[bool]
	IsExternalShareResultEnabled plugin.TValue[bool]
	IsExternalShareTemplateEnabled plugin.TValue[bool]
	IsRecordIdentityByDefaultEnabled plugin.TValue[bool]
	IsBingImageSearchEnabled plugin.TValue[bool]
	IsInOrgFormsPhishingScanEnabled plugin.TValue[bool]
}

// createMicrosoftTenantFormsSettings creates a new instance of this resource
func createMicrosoftTenantFormsSettings(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftTenantFormsSettings{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.tenant.formsSettings", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftTenantFormsSettings) MqlName() string {
	return "microsoft.tenant.formsSettings"
}

func (c *mqlMicrosoftTenantFormsSettings) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftTenantFormsSettings) GetIsExternalSendFormEnabled() *plugin.TValue[bool] {
	return &c.IsExternalSendFormEnabled
}

func (c *mqlMicrosoftTenantFormsSettings) GetIsExternalShareCollaborationEnabled() *plugin.TValue[bool] {
	return &c.IsExternalShareCollaborationEnabled
}

func (c *mqlMicrosoftTenantFormsSettings) GetIsExternalShareResultEnabled() *plugin.TValue[bool] {
	return &c.IsExternalShareResultEnabled
}

func (c *mqlMicrosoftTenantFormsSettings) GetIsExternalShareTemplateEnabled() *plugin.TValue[bool] {
	return &c.IsExternalShareTemplateEnabled
}

func (c *mqlMicrosoftTenantFormsSettings) GetIsRecordIdentityByDefaultEnabled() *plugin.TValue[bool] {
	return &c.IsRecordIdentityByDefaultEnabled
}

func (c *mqlMicrosoftTenantFormsSettings) GetIsBingImageSearchEnabled() *plugin.TValue[bool] {
	return &c.IsBingImageSearchEnabled
}

func (c *mqlMicrosoftTenantFormsSettings) GetIsInOrgFormsPhishingScanEnabled() *plugin.TValue[bool] {
	return &c.IsInOrgFormsPhishingScanEnabled
}

// mqlMicrosoftUsers for the microsoft.users resource
type mqlMicrosoftUsers struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUsersInternal it will be used here
	Filter plugin.TValue[string]
	Search plugin.TValue[string]
	List plugin.TValue[[]interface{}]
}

// createMicrosoftUsers creates a new instance of this resource
func createMicrosoftUsers(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUsers{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.users", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUsers) MqlName() string {
	return "microsoft.users"
}

func (c *mqlMicrosoftUsers) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUsers) GetFilter() *plugin.TValue[string] {
	return &c.Filter
}

func (c *mqlMicrosoftUsers) GetSearch() *plugin.TValue[string] {
	return &c.Search
}

func (c *mqlMicrosoftUsers) GetList() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.List, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.users", c.__id, "list")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.list()
	})
}

// mqlMicrosoftIdentityAndAccess for the microsoft.identityAndAccess resource
type mqlMicrosoftIdentityAndAccess struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftIdentityAndAccessInternal it will be used here
	Filter plugin.TValue[string]
	RoleEligibilityScheduleInstances plugin.TValue[[]interface{}]
	List plugin.TValue[[]interface{}]
}

// createMicrosoftIdentityAndAccess creates a new instance of this resource
func createMicrosoftIdentityAndAccess(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftIdentityAndAccess{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.identityAndAccess", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftIdentityAndAccess) MqlName() string {
	return "microsoft.identityAndAccess"
}

func (c *mqlMicrosoftIdentityAndAccess) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftIdentityAndAccess) GetFilter() *plugin.TValue[string] {
	return &c.Filter
}

func (c *mqlMicrosoftIdentityAndAccess) GetRoleEligibilityScheduleInstances() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.RoleEligibilityScheduleInstances, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.identityAndAccess", c.__id, "roleEligibilityScheduleInstances")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.roleEligibilityScheduleInstances()
	})
}

func (c *mqlMicrosoftIdentityAndAccess) GetList() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.List, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.identityAndAccess", c.__id, "list")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.list()
	})
}

// mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance for the microsoft.identityAndAccess.roleEligibilityScheduleInstance resource
type mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstanceInternal it will be used here
	Id plugin.TValue[string]
	PrincipalId plugin.TValue[string]
	RoleDefinitionId plugin.TValue[string]
	DirectoryScopeId plugin.TValue[string]
	AppScopeId plugin.TValue[string]
	StartDateTime plugin.TValue[*time.Time]
	EndDateTime plugin.TValue[*time.Time]
	MemberType plugin.TValue[string]
	RoleEligibilityScheduleId plugin.TValue[string]
}

// createMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance creates a new instance of this resource
func createMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.identityAndAccess.roleEligibilityScheduleInstance", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) MqlName() string {
	return "microsoft.identityAndAccess.roleEligibilityScheduleInstance"
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetPrincipalId() *plugin.TValue[string] {
	return &c.PrincipalId
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetRoleDefinitionId() *plugin.TValue[string] {
	return &c.RoleDefinitionId
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetDirectoryScopeId() *plugin.TValue[string] {
	return &c.DirectoryScopeId
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetAppScopeId() *plugin.TValue[string] {
	return &c.AppScopeId
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetStartDateTime() *plugin.TValue[*time.Time] {
	return &c.StartDateTime
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetEndDateTime() *plugin.TValue[*time.Time] {
	return &c.EndDateTime
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetMemberType() *plugin.TValue[string] {
	return &c.MemberType
}

func (c *mqlMicrosoftIdentityAndAccessRoleEligibilityScheduleInstance) GetRoleEligibilityScheduleId() *plugin.TValue[string] {
	return &c.RoleEligibilityScheduleId
}

// mqlMicrosoftIdentityAndAccessPolicy for the microsoft.identityAndAccess.policy resource
type mqlMicrosoftIdentityAndAccessPolicy struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftIdentityAndAccessPolicyInternal it will be used here
	Id plugin.TValue[string]
	DisplayName plugin.TValue[string]
	Description plugin.TValue[string]
	IsOrganizationDefault plugin.TValue[bool]
	ScopeId plugin.TValue[string]
	ScopeType plugin.TValue[string]
	LastModifiedDateTime plugin.TValue[*time.Time]
	LastModifiedBy plugin.TValue[interface{}]
	Rules plugin.TValue[[]interface{}]
}

// createMicrosoftIdentityAndAccessPolicy creates a new instance of this resource
func createMicrosoftIdentityAndAccessPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftIdentityAndAccessPolicy{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.identityAndAccess.policy", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) MqlName() string {
	return "microsoft.identityAndAccess.policy"
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetIsOrganizationDefault() *plugin.TValue[bool] {
	return &c.IsOrganizationDefault
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetScopeId() *plugin.TValue[string] {
	return &c.ScopeId
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetScopeType() *plugin.TValue[string] {
	return &c.ScopeType
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetLastModifiedDateTime() *plugin.TValue[*time.Time] {
	return &c.LastModifiedDateTime
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetLastModifiedBy() *plugin.TValue[interface{}] {
	return &c.LastModifiedBy
}

func (c *mqlMicrosoftIdentityAndAccessPolicy) GetRules() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Rules, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.identityAndAccess.policy", c.__id, "rules")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.rules()
	})
}

// mqlMicrosoftIdentityAndAccessPolicyRule for the microsoft.identityAndAccess.policy.rule resource
type mqlMicrosoftIdentityAndAccessPolicyRule struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftIdentityAndAccessPolicyRuleInternal it will be used here
	Id plugin.TValue[string]
	Target plugin.TValue[*mqlMicrosoftIdentityAndAccessPolicyRuleTarget]
}

// createMicrosoftIdentityAndAccessPolicyRule creates a new instance of this resource
func createMicrosoftIdentityAndAccessPolicyRule(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftIdentityAndAccessPolicyRule{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.identityAndAccess.policy.rule", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRule) MqlName() string {
	return "microsoft.identityAndAccess.policy.rule"
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRule) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRule) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRule) GetTarget() *plugin.TValue[*mqlMicrosoftIdentityAndAccessPolicyRuleTarget] {
	return &c.Target
}

// mqlMicrosoftIdentityAndAccessPolicyRuleTarget for the microsoft.identityAndAccess.policy.rule.target resource
type mqlMicrosoftIdentityAndAccessPolicyRuleTarget struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftIdentityAndAccessPolicyRuleTargetInternal it will be used here
	Caller plugin.TValue[string]
	EnforcedSettings plugin.TValue[[]interface{}]
	InheritableSettings plugin.TValue[[]interface{}]
	Level plugin.TValue[string]
	Operations plugin.TValue[[]interface{}]
}

// createMicrosoftIdentityAndAccessPolicyRuleTarget creates a new instance of this resource
func createMicrosoftIdentityAndAccessPolicyRuleTarget(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftIdentityAndAccessPolicyRuleTarget{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.identityAndAccess.policy.rule.target", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRuleTarget) MqlName() string {
	return "microsoft.identityAndAccess.policy.rule.target"
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRuleTarget) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRuleTarget) GetCaller() *plugin.TValue[string] {
	return &c.Caller
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRuleTarget) GetEnforcedSettings() *plugin.TValue[[]interface{}] {
	return &c.EnforcedSettings
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRuleTarget) GetInheritableSettings() *plugin.TValue[[]interface{}] {
	return &c.InheritableSettings
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRuleTarget) GetLevel() *plugin.TValue[string] {
	return &c.Level
}

func (c *mqlMicrosoftIdentityAndAccessPolicyRuleTarget) GetOperations() *plugin.TValue[[]interface{}] {
	return &c.Operations
}

// mqlMicrosoftUserAssignedLicense for the microsoft.user.assignedLicense resource
type mqlMicrosoftUserAssignedLicense struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserAssignedLicenseInternal it will be used here
	DisabledPlans plugin.TValue[[]interface{}]
	SkuId plugin.TValue[string]
}

// createMicrosoftUserAssignedLicense creates a new instance of this resource
func createMicrosoftUserAssignedLicense(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserAssignedLicense{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.assignedLicense", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserAssignedLicense) MqlName() string {
	return "microsoft.user.assignedLicense"
}

func (c *mqlMicrosoftUserAssignedLicense) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserAssignedLicense) GetDisabledPlans() *plugin.TValue[[]interface{}] {
	return &c.DisabledPlans
}

func (c *mqlMicrosoftUserAssignedLicense) GetSkuId() *plugin.TValue[string] {
	return &c.SkuId
}

// mqlMicrosoftUserLicenseDetail for the microsoft.user.licenseDetail resource
type mqlMicrosoftUserLicenseDetail struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserLicenseDetailInternal it will be used here
	Id plugin.TValue[string]
	SkuId plugin.TValue[string]
	SkuPartNumber plugin.TValue[string]
	ServicePlans plugin.TValue[[]interface{}]
}

// createMicrosoftUserLicenseDetail creates a new instance of this resource
func createMicrosoftUserLicenseDetail(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserLicenseDetail{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.licenseDetail", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserLicenseDetail) MqlName() string {
	return "microsoft.user.licenseDetail"
}

func (c *mqlMicrosoftUserLicenseDetail) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserLicenseDetail) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftUserLicenseDetail) GetSkuId() *plugin.TValue[string] {
	return &c.SkuId
}

func (c *mqlMicrosoftUserLicenseDetail) GetSkuPartNumber() *plugin.TValue[string] {
	return &c.SkuPartNumber
}

func (c *mqlMicrosoftUserLicenseDetail) GetServicePlans() *plugin.TValue[[]interface{}] {
	return &c.ServicePlans
}

// mqlMicrosoftUserLicenseDetailServicePlanInfo for the microsoft.user.licenseDetail.servicePlanInfo resource
type mqlMicrosoftUserLicenseDetailServicePlanInfo struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserLicenseDetailServicePlanInfoInternal it will be used here
	AppliesTo plugin.TValue[string]
	ProvisioningStatus plugin.TValue[string]
	ServicePlanId plugin.TValue[string]
	ServicePlanName plugin.TValue[string]
}

// createMicrosoftUserLicenseDetailServicePlanInfo creates a new instance of this resource
func createMicrosoftUserLicenseDetailServicePlanInfo(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserLicenseDetailServicePlanInfo{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.licenseDetail.servicePlanInfo", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserLicenseDetailServicePlanInfo) MqlName() string {
	return "microsoft.user.licenseDetail.servicePlanInfo"
}

func (c *mqlMicrosoftUserLicenseDetailServicePlanInfo) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserLicenseDetailServicePlanInfo) GetAppliesTo() *plugin.TValue[string] {
	return &c.AppliesTo
}

func (c *mqlMicrosoftUserLicenseDetailServicePlanInfo) GetProvisioningStatus() *plugin.TValue[string] {
	return &c.ProvisioningStatus
}

func (c *mqlMicrosoftUserLicenseDetailServicePlanInfo) GetServicePlanId() *plugin.TValue[string] {
	return &c.ServicePlanId
}

func (c *mqlMicrosoftUserLicenseDetailServicePlanInfo) GetServicePlanName() *plugin.TValue[string] {
	return &c.ServicePlanName
}

// mqlMicrosoftConditionalAccess for the microsoft.conditionalAccess resource
type mqlMicrosoftConditionalAccess struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessInternal it will be used here
	NamedLocations plugin.TValue[*mqlMicrosoftConditionalAccessNamedLocations]
	Policies plugin.TValue[[]interface{}]
	AuthenticationMethodsPolicy plugin.TValue[*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy]
}

// createMicrosoftConditionalAccess creates a new instance of this resource
func createMicrosoftConditionalAccess(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccess{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccess) MqlName() string {
	return "microsoft.conditionalAccess"
}

func (c *mqlMicrosoftConditionalAccess) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccess) GetNamedLocations() *plugin.TValue[*mqlMicrosoftConditionalAccessNamedLocations] {
	return &c.NamedLocations
}

func (c *mqlMicrosoftConditionalAccess) GetPolicies() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Policies, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.conditionalAccess", c.__id, "policies")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.policies()
	})
}

func (c *mqlMicrosoftConditionalAccess) GetAuthenticationMethodsPolicy() *plugin.TValue[*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy] {
	return plugin.GetOrCompute[*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy](&c.AuthenticationMethodsPolicy, func() (*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.conditionalAccess", c.__id, "authenticationMethodsPolicy")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy), nil
			}
		}

		return c.authenticationMethodsPolicy()
	})
}

// mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy for the microsoft.conditionalAccess.authenticationMethodsPolicy resource
type mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessAuthenticationMethodsPolicyInternal it will be used here
	Id plugin.TValue[string]
	DisplayName plugin.TValue[string]
	Description plugin.TValue[string]
	LastModifiedDateTime plugin.TValue[*time.Time]
	PolicyVersion plugin.TValue[string]
	AuthenticationMethodConfigurations plugin.TValue[[]interface{}]
}

// createMicrosoftConditionalAccessAuthenticationMethodsPolicy creates a new instance of this resource
func createMicrosoftConditionalAccessAuthenticationMethodsPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.authenticationMethodsPolicy", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy) MqlName() string {
	return "microsoft.conditionalAccess.authenticationMethodsPolicy"
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy) GetLastModifiedDateTime() *plugin.TValue[*time.Time] {
	return &c.LastModifiedDateTime
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy) GetPolicyVersion() *plugin.TValue[string] {
	return &c.PolicyVersion
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodsPolicy) GetAuthenticationMethodConfigurations() *plugin.TValue[[]interface{}] {
	return &c.AuthenticationMethodConfigurations
}

// mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration for the microsoft.conditionalAccess.authenticationMethodConfiguration resource
type mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessAuthenticationMethodConfigurationInternal it will be used here
	Id plugin.TValue[string]
	State plugin.TValue[string]
}

// createMicrosoftConditionalAccessAuthenticationMethodConfiguration creates a new instance of this resource
func createMicrosoftConditionalAccessAuthenticationMethodConfiguration(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.authenticationMethodConfiguration", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration) MqlName() string {
	return "microsoft.conditionalAccess.authenticationMethodConfiguration"
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftConditionalAccessAuthenticationMethodConfiguration) GetState() *plugin.TValue[string] {
	return &c.State
}

// mqlMicrosoftConditionalAccessNamedLocations for the microsoft.conditionalAccess.namedLocations resource
type mqlMicrosoftConditionalAccessNamedLocations struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessNamedLocationsInternal it will be used here
	IpLocations plugin.TValue[[]interface{}]
	CountryLocations plugin.TValue[[]interface{}]
}

// createMicrosoftConditionalAccessNamedLocations creates a new instance of this resource
func createMicrosoftConditionalAccessNamedLocations(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessNamedLocations{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.namedLocations", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessNamedLocations) MqlName() string {
	return "microsoft.conditionalAccess.namedLocations"
}

func (c *mqlMicrosoftConditionalAccessNamedLocations) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessNamedLocations) GetIpLocations() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.IpLocations, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.conditionalAccess.namedLocations", c.__id, "ipLocations")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.ipLocations()
	})
}

func (c *mqlMicrosoftConditionalAccessNamedLocations) GetCountryLocations() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.CountryLocations, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.conditionalAccess.namedLocations", c.__id, "countryLocations")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.countryLocations()
	})
}

// mqlMicrosoftConditionalAccessPolicy for the microsoft.conditionalAccess.policy resource
type mqlMicrosoftConditionalAccessPolicy struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyInternal it will be used here
	Id plugin.TValue[string]
	DisplayName plugin.TValue[string]
	State plugin.TValue[string]
	CreatedDateTime plugin.TValue[*time.Time]
	ModifiedDateTime plugin.TValue[*time.Time]
	Conditions plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditions]
	GrantControls plugin.TValue[*mqlMicrosoftConditionalAccessPolicyGrantControls]
	SessionControls plugin.TValue[*mqlMicrosoftConditionalAccessPolicySessionControls]
	TemplateId plugin.TValue[string]
}

// createMicrosoftConditionalAccessPolicy creates a new instance of this resource
func createMicrosoftConditionalAccessPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicy{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicy) MqlName() string {
	return "microsoft.conditionalAccess.policy"
}

func (c *mqlMicrosoftConditionalAccessPolicy) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetState() *plugin.TValue[string] {
	return &c.State
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetModifiedDateTime() *plugin.TValue[*time.Time] {
	return &c.ModifiedDateTime
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetConditions() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditions] {
	return &c.Conditions
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetGrantControls() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyGrantControls] {
	return &c.GrantControls
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetSessionControls() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicySessionControls] {
	return &c.SessionControls
}

func (c *mqlMicrosoftConditionalAccessPolicy) GetTemplateId() *plugin.TValue[string] {
	return &c.TemplateId
}

// mqlMicrosoftConditionalAccessPolicyConditions for the microsoft.conditionalAccess.policy.conditions resource
type mqlMicrosoftConditionalAccessPolicyConditions struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyConditionsInternal it will be used here
	Id plugin.TValue[string]
	Applications plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsApplications]
	AuthenticationFlows plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows]
	ClientApplications plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsClientApplications]
	ClientAppTypes plugin.TValue[[]interface{}]
	Locations plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsLocations]
	Platforms plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsPlatforms]
	ServicePrincipalRiskLevels plugin.TValue[[]interface{}]
	SignInRiskLevels plugin.TValue[[]interface{}]
	UserRiskLevels plugin.TValue[[]interface{}]
	Users plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsUsers]
	InsiderRiskLevels plugin.TValue[string]
}

// createMicrosoftConditionalAccessPolicyConditions creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyConditions(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyConditions{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.conditions", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) MqlName() string {
	return "microsoft.conditionalAccess.policy.conditions"
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetApplications() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsApplications] {
	return &c.Applications
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetAuthenticationFlows() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows] {
	return &c.AuthenticationFlows
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetClientApplications() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsClientApplications] {
	return &c.ClientApplications
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetClientAppTypes() *plugin.TValue[[]interface{}] {
	return &c.ClientAppTypes
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetLocations() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsLocations] {
	return &c.Locations
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetPlatforms() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsPlatforms] {
	return &c.Platforms
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetServicePrincipalRiskLevels() *plugin.TValue[[]interface{}] {
	return &c.ServicePrincipalRiskLevels
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetSignInRiskLevels() *plugin.TValue[[]interface{}] {
	return &c.SignInRiskLevels
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetUserRiskLevels() *plugin.TValue[[]interface{}] {
	return &c.UserRiskLevels
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetUsers() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyConditionsUsers] {
	return &c.Users
}

func (c *mqlMicrosoftConditionalAccessPolicyConditions) GetInsiderRiskLevels() *plugin.TValue[string] {
	return &c.InsiderRiskLevels
}

// mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows for the microsoft.conditionalAccess.policy.conditions.authenticationFlows resource
type mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlowsInternal it will be used here
	TransferMethods plugin.TValue[string]
}

// createMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.conditions.authenticationFlows", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows) MqlName() string {
	return "microsoft.conditionalAccess.policy.conditions.authenticationFlows"
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsAuthenticationFlows) GetTransferMethods() *plugin.TValue[string] {
	return &c.TransferMethods
}

// mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength for the microsoft.conditionalAccess.policy.grantControls.authenticationStrength resource
type mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrengthInternal it will be used here
	Id plugin.TValue[string]
	AllowedCombinations plugin.TValue[[]interface{}]
	DisplayName plugin.TValue[string]
	Description plugin.TValue[string]
	PolicyType plugin.TValue[string]
	RequirementsSatisfied plugin.TValue[string]
	CreatedDateTime plugin.TValue[*time.Time]
	ModifiedDateTime plugin.TValue[*time.Time]
}

// createMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.grantControls.authenticationStrength", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) MqlName() string {
	return "microsoft.conditionalAccess.policy.grantControls.authenticationStrength"
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) GetAllowedCombinations() *plugin.TValue[[]interface{}] {
	return &c.AllowedCombinations
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) GetPolicyType() *plugin.TValue[string] {
	return &c.PolicyType
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) GetRequirementsSatisfied() *plugin.TValue[string] {
	return &c.RequirementsSatisfied
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength) GetModifiedDateTime() *plugin.TValue[*time.Time] {
	return &c.ModifiedDateTime
}

// mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency for the microsoft.conditionalAccess.policy.sessionControls.signInFrequency resource
type mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequencyInternal it will be used here
	AuthenticationType plugin.TValue[string]
	FrequencyInterval plugin.TValue[string]
	IsEnabled plugin.TValue[bool]
}

// createMicrosoftConditionalAccessPolicySessionControlsSignInFrequency creates a new instance of this resource
func createMicrosoftConditionalAccessPolicySessionControlsSignInFrequency(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.sessionControls.signInFrequency", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency) MqlName() string {
	return "microsoft.conditionalAccess.policy.sessionControls.signInFrequency"
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency) GetAuthenticationType() *plugin.TValue[string] {
	return &c.AuthenticationType
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency) GetFrequencyInterval() *plugin.TValue[string] {
	return &c.FrequencyInterval
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency) GetIsEnabled() *plugin.TValue[bool] {
	return &c.IsEnabled
}

// mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity for the microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity resource
type mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurityInternal it will be used here
	CloudAppSecurityType plugin.TValue[string]
	IsEnabled plugin.TValue[bool]
}

// createMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity creates a new instance of this resource
func createMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity) MqlName() string {
	return "microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity"
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity) GetCloudAppSecurityType() *plugin.TValue[string] {
	return &c.CloudAppSecurityType
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity) GetIsEnabled() *plugin.TValue[bool] {
	return &c.IsEnabled
}

// mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser for the microsoft.conditionalAccess.policy.sessionControls.persistentBrowser resource
type mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowserInternal it will be used here
	Mode plugin.TValue[string]
	IsEnabled plugin.TValue[bool]
}

// createMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser creates a new instance of this resource
func createMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.sessionControls.persistentBrowser", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser) MqlName() string {
	return "microsoft.conditionalAccess.policy.sessionControls.persistentBrowser"
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser) GetMode() *plugin.TValue[string] {
	return &c.Mode
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsPersistentBrowser) GetIsEnabled() *plugin.TValue[bool] {
	return &c.IsEnabled
}

// mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions for the microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions resource
type mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictionsInternal it will be used here
	IsEnabled plugin.TValue[bool]
}

// createMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions creates a new instance of this resource
func createMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions) MqlName() string {
	return "microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions"
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions) GetIsEnabled() *plugin.TValue[bool] {
	return &c.IsEnabled
}

// mqlMicrosoftConditionalAccessPolicyConditionsClientApplications for the microsoft.conditionalAccess.policy.conditions.clientApplications resource
type mqlMicrosoftConditionalAccessPolicyConditionsClientApplications struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyConditionsClientApplicationsInternal it will be used here
	ExcludeServicePrincipals plugin.TValue[[]interface{}]
	IncludeServicePrincipals plugin.TValue[[]interface{}]
}

// createMicrosoftConditionalAccessPolicyConditionsClientApplications creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyConditionsClientApplications(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyConditionsClientApplications{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.conditions.clientApplications", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsClientApplications) MqlName() string {
	return "microsoft.conditionalAccess.policy.conditions.clientApplications"
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsClientApplications) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsClientApplications) GetExcludeServicePrincipals() *plugin.TValue[[]interface{}] {
	return &c.ExcludeServicePrincipals
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsClientApplications) GetIncludeServicePrincipals() *plugin.TValue[[]interface{}] {
	return &c.IncludeServicePrincipals
}

// mqlMicrosoftConditionalAccessPolicyConditionsPlatforms for the microsoft.conditionalAccess.policy.conditions.platforms resource
type mqlMicrosoftConditionalAccessPolicyConditionsPlatforms struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyConditionsPlatformsInternal it will be used here
	ExcludePlatforms plugin.TValue[[]interface{}]
	IncludePlatforms plugin.TValue[[]interface{}]
}

// createMicrosoftConditionalAccessPolicyConditionsPlatforms creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyConditionsPlatforms(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyConditionsPlatforms{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.conditions.platforms", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsPlatforms) MqlName() string {
	return "microsoft.conditionalAccess.policy.conditions.platforms"
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsPlatforms) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsPlatforms) GetExcludePlatforms() *plugin.TValue[[]interface{}] {
	return &c.ExcludePlatforms
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsPlatforms) GetIncludePlatforms() *plugin.TValue[[]interface{}] {
	return &c.IncludePlatforms
}

// mqlMicrosoftConditionalAccessPolicyConditionsApplications for the microsoft.conditionalAccess.policy.conditions.applications resource
type mqlMicrosoftConditionalAccessPolicyConditionsApplications struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyConditionsApplicationsInternal it will be used here
	IncludeApplications plugin.TValue[[]interface{}]
	ExcludeApplications plugin.TValue[[]interface{}]
	IncludeUserActions plugin.TValue[[]interface{}]
}

// createMicrosoftConditionalAccessPolicyConditionsApplications creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyConditionsApplications(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyConditionsApplications{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.conditions.applications", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsApplications) MqlName() string {
	return "microsoft.conditionalAccess.policy.conditions.applications"
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsApplications) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsApplications) GetIncludeApplications() *plugin.TValue[[]interface{}] {
	return &c.IncludeApplications
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsApplications) GetExcludeApplications() *plugin.TValue[[]interface{}] {
	return &c.ExcludeApplications
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsApplications) GetIncludeUserActions() *plugin.TValue[[]interface{}] {
	return &c.IncludeUserActions
}

// mqlMicrosoftConditionalAccessPolicyConditionsUsers for the microsoft.conditionalAccess.policy.conditions.users resource
type mqlMicrosoftConditionalAccessPolicyConditionsUsers struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyConditionsUsersInternal it will be used here
	IncludeUsers plugin.TValue[[]interface{}]
	ExcludeUsers plugin.TValue[[]interface{}]
	IncludeGroups plugin.TValue[[]interface{}]
	ExcludeGroups plugin.TValue[[]interface{}]
	IncludeRoles plugin.TValue[[]interface{}]
	ExcludeRoles plugin.TValue[[]interface{}]
}

// createMicrosoftConditionalAccessPolicyConditionsUsers creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyConditionsUsers(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyConditionsUsers{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.conditions.users", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsUsers) MqlName() string {
	return "microsoft.conditionalAccess.policy.conditions.users"
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsUsers) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsUsers) GetIncludeUsers() *plugin.TValue[[]interface{}] {
	return &c.IncludeUsers
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsUsers) GetExcludeUsers() *plugin.TValue[[]interface{}] {
	return &c.ExcludeUsers
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsUsers) GetIncludeGroups() *plugin.TValue[[]interface{}] {
	return &c.IncludeGroups
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsUsers) GetExcludeGroups() *plugin.TValue[[]interface{}] {
	return &c.ExcludeGroups
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsUsers) GetIncludeRoles() *plugin.TValue[[]interface{}] {
	return &c.IncludeRoles
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsUsers) GetExcludeRoles() *plugin.TValue[[]interface{}] {
	return &c.ExcludeRoles
}

// mqlMicrosoftConditionalAccessPolicyConditionsLocations for the microsoft.conditionalAccess.policy.conditions.locations resource
type mqlMicrosoftConditionalAccessPolicyConditionsLocations struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyConditionsLocationsInternal it will be used here
	IncludeLocations plugin.TValue[[]interface{}]
	ExcludeLocations plugin.TValue[[]interface{}]
}

// createMicrosoftConditionalAccessPolicyConditionsLocations creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyConditionsLocations(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyConditionsLocations{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.conditions.locations", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsLocations) MqlName() string {
	return "microsoft.conditionalAccess.policy.conditions.locations"
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsLocations) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsLocations) GetIncludeLocations() *plugin.TValue[[]interface{}] {
	return &c.IncludeLocations
}

func (c *mqlMicrosoftConditionalAccessPolicyConditionsLocations) GetExcludeLocations() *plugin.TValue[[]interface{}] {
	return &c.ExcludeLocations
}

// mqlMicrosoftConditionalAccessPolicyGrantControls for the microsoft.conditionalAccess.policy.grantControls resource
type mqlMicrosoftConditionalAccessPolicyGrantControls struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicyGrantControlsInternal it will be used here
	Id plugin.TValue[string]
	Operator plugin.TValue[string]
	BuiltInControls plugin.TValue[[]interface{}]
	AuthenticationStrength plugin.TValue[*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength]
	CustomAuthenticationFactors plugin.TValue[[]interface{}]
	TermsOfUse plugin.TValue[[]interface{}]
}

// createMicrosoftConditionalAccessPolicyGrantControls creates a new instance of this resource
func createMicrosoftConditionalAccessPolicyGrantControls(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicyGrantControls{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.grantControls", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControls) MqlName() string {
	return "microsoft.conditionalAccess.policy.grantControls"
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControls) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControls) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControls) GetOperator() *plugin.TValue[string] {
	return &c.Operator
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControls) GetBuiltInControls() *plugin.TValue[[]interface{}] {
	return &c.BuiltInControls
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControls) GetAuthenticationStrength() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicyGrantControlsAuthenticationStrength] {
	return &c.AuthenticationStrength
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControls) GetCustomAuthenticationFactors() *plugin.TValue[[]interface{}] {
	return &c.CustomAuthenticationFactors
}

func (c *mqlMicrosoftConditionalAccessPolicyGrantControls) GetTermsOfUse() *plugin.TValue[[]interface{}] {
	return &c.TermsOfUse
}

// mqlMicrosoftConditionalAccessPolicySessionControls for the microsoft.conditionalAccess.policy.sessionControls resource
type mqlMicrosoftConditionalAccessPolicySessionControls struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessPolicySessionControlsInternal it will be used here
	Id plugin.TValue[string]
	SignInFrequency plugin.TValue[*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency]
	CloudAppSecurity plugin.TValue[*mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity]
	PersistentBrowser plugin.TValue[interface{}]
	ApplicationEnforcedRestrictions plugin.TValue[*mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions]
	SecureSignInSession plugin.TValue[interface{}]
}

// createMicrosoftConditionalAccessPolicySessionControls creates a new instance of this resource
func createMicrosoftConditionalAccessPolicySessionControls(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessPolicySessionControls{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.policy.sessionControls", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControls) MqlName() string {
	return "microsoft.conditionalAccess.policy.sessionControls"
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControls) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControls) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControls) GetSignInFrequency() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicySessionControlsSignInFrequency] {
	return &c.SignInFrequency
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControls) GetCloudAppSecurity() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicySessionControlsCloudAppSecurity] {
	return &c.CloudAppSecurity
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControls) GetPersistentBrowser() *plugin.TValue[interface{}] {
	return &c.PersistentBrowser
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControls) GetApplicationEnforcedRestrictions() *plugin.TValue[*mqlMicrosoftConditionalAccessPolicySessionControlsApplicationEnforcedRestrictions] {
	return &c.ApplicationEnforcedRestrictions
}

func (c *mqlMicrosoftConditionalAccessPolicySessionControls) GetSecureSignInSession() *plugin.TValue[interface{}] {
	return &c.SecureSignInSession
}

// mqlMicrosoftConditionalAccessIpNamedLocation for the microsoft.conditionalAccess.ipNamedLocation resource
type mqlMicrosoftConditionalAccessIpNamedLocation struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessIpNamedLocationInternal it will be used here
	Name plugin.TValue[string]
	Trusted plugin.TValue[bool]
}

// createMicrosoftConditionalAccessIpNamedLocation creates a new instance of this resource
func createMicrosoftConditionalAccessIpNamedLocation(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessIpNamedLocation{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.ipNamedLocation", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessIpNamedLocation) MqlName() string {
	return "microsoft.conditionalAccess.ipNamedLocation"
}

func (c *mqlMicrosoftConditionalAccessIpNamedLocation) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessIpNamedLocation) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftConditionalAccessIpNamedLocation) GetTrusted() *plugin.TValue[bool] {
	return &c.Trusted
}

// mqlMicrosoftConditionalAccessCountryNamedLocation for the microsoft.conditionalAccess.countryNamedLocation resource
type mqlMicrosoftConditionalAccessCountryNamedLocation struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftConditionalAccessCountryNamedLocationInternal it will be used here
	Name plugin.TValue[string]
	LookupMethod plugin.TValue[string]
}

// createMicrosoftConditionalAccessCountryNamedLocation creates a new instance of this resource
func createMicrosoftConditionalAccessCountryNamedLocation(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftConditionalAccessCountryNamedLocation{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.conditionalAccess.countryNamedLocation", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftConditionalAccessCountryNamedLocation) MqlName() string {
	return "microsoft.conditionalAccess.countryNamedLocation"
}

func (c *mqlMicrosoftConditionalAccessCountryNamedLocation) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftConditionalAccessCountryNamedLocation) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftConditionalAccessCountryNamedLocation) GetLookupMethod() *plugin.TValue[string] {
	return &c.LookupMethod
}

// mqlMicrosoftUser for the microsoft.user resource
type mqlMicrosoftUser struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserInternal it will be used here
	Id plugin.TValue[string]
	AccountEnabled plugin.TValue[bool]
	City plugin.TValue[string]
	CompanyName plugin.TValue[string]
	Country plugin.TValue[string]
	CreatedDateTime plugin.TValue[*time.Time]
	Department plugin.TValue[string]
	DisplayName plugin.TValue[string]
	EmployeeId plugin.TValue[string]
	GivenName plugin.TValue[string]
	JobTitle plugin.TValue[string]
	Mail plugin.TValue[string]
	MobilePhone plugin.TValue[string]
	OtherMails plugin.TValue[[]interface{}]
	OfficeLocation plugin.TValue[string]
	PostalCode plugin.TValue[string]
	State plugin.TValue[string]
	StreetAddress plugin.TValue[string]
	Surname plugin.TValue[string]
	UserPrincipalName plugin.TValue[string]
	UserType plugin.TValue[string]
	Settings plugin.TValue[interface{}]
	Job plugin.TValue[interface{}]
	Contact plugin.TValue[interface{}]
	AuthMethods plugin.TValue[*mqlMicrosoftUserAuthenticationMethods]
	MfaEnabled plugin.TValue[bool]
	CreationType plugin.TValue[string]
	Identities plugin.TValue[[]interface{}]
	Auditlog plugin.TValue[*mqlMicrosoftUserAuditlog]
	AssignedLicenses plugin.TValue[[]interface{}]
	LicenseDetails plugin.TValue[[]interface{}]
	AuthenticationRequirements plugin.TValue[*mqlMicrosoftUserAuthenticationRequirements]
}

// createMicrosoftUser creates a new instance of this resource
func createMicrosoftUser(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUser{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUser) MqlName() string {
	return "microsoft.user"
}

func (c *mqlMicrosoftUser) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUser) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftUser) GetAccountEnabled() *plugin.TValue[bool] {
	return &c.AccountEnabled
}

func (c *mqlMicrosoftUser) GetCity() *plugin.TValue[string] {
	return &c.City
}

func (c *mqlMicrosoftUser) GetCompanyName() *plugin.TValue[string] {
	return &c.CompanyName
}

func (c *mqlMicrosoftUser) GetCountry() *plugin.TValue[string] {
	return &c.Country
}

func (c *mqlMicrosoftUser) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftUser) GetDepartment() *plugin.TValue[string] {
	return &c.Department
}

func (c *mqlMicrosoftUser) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftUser) GetEmployeeId() *plugin.TValue[string] {
	return &c.EmployeeId
}

func (c *mqlMicrosoftUser) GetGivenName() *plugin.TValue[string] {
	return &c.GivenName
}

func (c *mqlMicrosoftUser) GetJobTitle() *plugin.TValue[string] {
	return &c.JobTitle
}

func (c *mqlMicrosoftUser) GetMail() *plugin.TValue[string] {
	return &c.Mail
}

func (c *mqlMicrosoftUser) GetMobilePhone() *plugin.TValue[string] {
	return &c.MobilePhone
}

func (c *mqlMicrosoftUser) GetOtherMails() *plugin.TValue[[]interface{}] {
	return &c.OtherMails
}

func (c *mqlMicrosoftUser) GetOfficeLocation() *plugin.TValue[string] {
	return &c.OfficeLocation
}

func (c *mqlMicrosoftUser) GetPostalCode() *plugin.TValue[string] {
	return &c.PostalCode
}

func (c *mqlMicrosoftUser) GetState() *plugin.TValue[string] {
	return &c.State
}

func (c *mqlMicrosoftUser) GetStreetAddress() *plugin.TValue[string] {
	return &c.StreetAddress
}

func (c *mqlMicrosoftUser) GetSurname() *plugin.TValue[string] {
	return &c.Surname
}

func (c *mqlMicrosoftUser) GetUserPrincipalName() *plugin.TValue[string] {
	return &c.UserPrincipalName
}

func (c *mqlMicrosoftUser) GetUserType() *plugin.TValue[string] {
	return &c.UserType
}

func (c *mqlMicrosoftUser) GetSettings() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.Settings, func() (interface{}, error) {
		return c.settings()
	})
}

func (c *mqlMicrosoftUser) GetJob() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.Job, func() (interface{}, error) {
		return c.job()
	})
}

func (c *mqlMicrosoftUser) GetContact() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.Contact, func() (interface{}, error) {
		return c.contact()
	})
}

func (c *mqlMicrosoftUser) GetAuthMethods() *plugin.TValue[*mqlMicrosoftUserAuthenticationMethods] {
	return plugin.GetOrCompute[*mqlMicrosoftUserAuthenticationMethods](&c.AuthMethods, func() (*mqlMicrosoftUserAuthenticationMethods, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.user", c.__id, "authMethods")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUserAuthenticationMethods), nil
			}
		}

		return c.authMethods()
	})
}

func (c *mqlMicrosoftUser) GetMfaEnabled() *plugin.TValue[bool] {
	return plugin.GetOrCompute[bool](&c.MfaEnabled, func() (bool, error) {
		return c.mfaEnabled()
	})
}

func (c *mqlMicrosoftUser) GetCreationType() *plugin.TValue[string] {
	return &c.CreationType
}

func (c *mqlMicrosoftUser) GetIdentities() *plugin.TValue[[]interface{}] {
	return &c.Identities
}

func (c *mqlMicrosoftUser) GetAuditlog() *plugin.TValue[*mqlMicrosoftUserAuditlog] {
	return plugin.GetOrCompute[*mqlMicrosoftUserAuditlog](&c.Auditlog, func() (*mqlMicrosoftUserAuditlog, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.user", c.__id, "auditlog")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUserAuditlog), nil
			}
		}

		return c.auditlog()
	})
}

func (c *mqlMicrosoftUser) GetAssignedLicenses() *plugin.TValue[[]interface{}] {
	return &c.AssignedLicenses
}

func (c *mqlMicrosoftUser) GetLicenseDetails() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.LicenseDetails, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.user", c.__id, "licenseDetails")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.licenseDetails()
	})
}

func (c *mqlMicrosoftUser) GetAuthenticationRequirements() *plugin.TValue[*mqlMicrosoftUserAuthenticationRequirements] {
	return plugin.GetOrCompute[*mqlMicrosoftUserAuthenticationRequirements](&c.AuthenticationRequirements, func() (*mqlMicrosoftUserAuthenticationRequirements, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.user", c.__id, "authenticationRequirements")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUserAuthenticationRequirements), nil
			}
		}

		return c.authenticationRequirements()
	})
}

// mqlMicrosoftUserAuthenticationRequirements for the microsoft.user.authenticationRequirements resource
type mqlMicrosoftUserAuthenticationRequirements struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserAuthenticationRequirementsInternal it will be used here
	PerUserMfaState plugin.TValue[string]
}

// createMicrosoftUserAuthenticationRequirements creates a new instance of this resource
func createMicrosoftUserAuthenticationRequirements(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserAuthenticationRequirements{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.authenticationRequirements", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserAuthenticationRequirements) MqlName() string {
	return "microsoft.user.authenticationRequirements"
}

func (c *mqlMicrosoftUserAuthenticationRequirements) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserAuthenticationRequirements) GetPerUserMfaState() *plugin.TValue[string] {
	return &c.PerUserMfaState
}

// mqlMicrosoftUserAuditlog for the microsoft.user.auditlog resource
type mqlMicrosoftUserAuditlog struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserAuditlogInternal it will be used here
	UserId plugin.TValue[string]
	Signins plugin.TValue[[]interface{}]
	LastInteractiveSignIn plugin.TValue[*mqlMicrosoftUserSignin]
	LastNonInteractiveSignIn plugin.TValue[*mqlMicrosoftUserSignin]
}

// createMicrosoftUserAuditlog creates a new instance of this resource
func createMicrosoftUserAuditlog(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserAuditlog{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.auditlog", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserAuditlog) MqlName() string {
	return "microsoft.user.auditlog"
}

func (c *mqlMicrosoftUserAuditlog) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserAuditlog) GetUserId() *plugin.TValue[string] {
	return &c.UserId
}

func (c *mqlMicrosoftUserAuditlog) GetSignins() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Signins, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.user.auditlog", c.__id, "signins")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.signins()
	})
}

func (c *mqlMicrosoftUserAuditlog) GetLastInteractiveSignIn() *plugin.TValue[*mqlMicrosoftUserSignin] {
	return plugin.GetOrCompute[*mqlMicrosoftUserSignin](&c.LastInteractiveSignIn, func() (*mqlMicrosoftUserSignin, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.user.auditlog", c.__id, "lastInteractiveSignIn")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUserSignin), nil
			}
		}

		return c.lastInteractiveSignIn()
	})
}

func (c *mqlMicrosoftUserAuditlog) GetLastNonInteractiveSignIn() *plugin.TValue[*mqlMicrosoftUserSignin] {
	return plugin.GetOrCompute[*mqlMicrosoftUserSignin](&c.LastNonInteractiveSignIn, func() (*mqlMicrosoftUserSignin, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.user.auditlog", c.__id, "lastNonInteractiveSignIn")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUserSignin), nil
			}
		}

		return c.lastNonInteractiveSignIn()
	})
}

// mqlMicrosoftUserIdentity for the microsoft.user.identity resource
type mqlMicrosoftUserIdentity struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserIdentityInternal it will be used here
	IssuerAssignedId plugin.TValue[string]
	Issuer plugin.TValue[string]
	SignInType plugin.TValue[string]
}

// createMicrosoftUserIdentity creates a new instance of this resource
func createMicrosoftUserIdentity(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserIdentity{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.identity", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserIdentity) MqlName() string {
	return "microsoft.user.identity"
}

func (c *mqlMicrosoftUserIdentity) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserIdentity) GetIssuerAssignedId() *plugin.TValue[string] {
	return &c.IssuerAssignedId
}

func (c *mqlMicrosoftUserIdentity) GetIssuer() *plugin.TValue[string] {
	return &c.Issuer
}

func (c *mqlMicrosoftUserIdentity) GetSignInType() *plugin.TValue[string] {
	return &c.SignInType
}

// mqlMicrosoftUserSignin for the microsoft.user.signin resource
type mqlMicrosoftUserSignin struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserSigninInternal it will be used here
	Id plugin.TValue[string]
	CreatedDateTime plugin.TValue[*time.Time]
	UserId plugin.TValue[string]
	UserDisplayName plugin.TValue[string]
	ClientAppUsed plugin.TValue[string]
	AppDisplayName plugin.TValue[string]
	ResourceDisplayName plugin.TValue[string]
	Interactive plugin.TValue[bool]
}

// createMicrosoftUserSignin creates a new instance of this resource
func createMicrosoftUserSignin(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserSignin{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.signin", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserSignin) MqlName() string {
	return "microsoft.user.signin"
}

func (c *mqlMicrosoftUserSignin) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserSignin) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftUserSignin) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftUserSignin) GetUserId() *plugin.TValue[string] {
	return &c.UserId
}

func (c *mqlMicrosoftUserSignin) GetUserDisplayName() *plugin.TValue[string] {
	return &c.UserDisplayName
}

func (c *mqlMicrosoftUserSignin) GetClientAppUsed() *plugin.TValue[string] {
	return &c.ClientAppUsed
}

func (c *mqlMicrosoftUserSignin) GetAppDisplayName() *plugin.TValue[string] {
	return &c.AppDisplayName
}

func (c *mqlMicrosoftUserSignin) GetResourceDisplayName() *plugin.TValue[string] {
	return &c.ResourceDisplayName
}

func (c *mqlMicrosoftUserSignin) GetInteractive() *plugin.TValue[bool] {
	return &c.Interactive
}

// mqlMicrosoftUserAuthenticationMethods for the microsoft.user.authenticationMethods resource
type mqlMicrosoftUserAuthenticationMethods struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserAuthenticationMethodsInternal it will be used here
	Count plugin.TValue[int64]
	PhoneMethods plugin.TValue[[]interface{}]
	EmailMethods plugin.TValue[[]interface{}]
	Fido2Methods plugin.TValue[[]interface{}]
	SoftwareMethods plugin.TValue[[]interface{}]
	MicrosoftAuthenticator plugin.TValue[[]interface{}]
	PasswordMethods plugin.TValue[[]interface{}]
	TemporaryAccessPassMethods plugin.TValue[[]interface{}]
	WindowsHelloMethods plugin.TValue[[]interface{}]
	RegistrationDetails plugin.TValue[*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails]
}

// createMicrosoftUserAuthenticationMethods creates a new instance of this resource
func createMicrosoftUserAuthenticationMethods(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserAuthenticationMethods{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.authenticationMethods", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserAuthenticationMethods) MqlName() string {
	return "microsoft.user.authenticationMethods"
}

func (c *mqlMicrosoftUserAuthenticationMethods) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetCount() *plugin.TValue[int64] {
	return &c.Count
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetPhoneMethods() *plugin.TValue[[]interface{}] {
	return &c.PhoneMethods
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetEmailMethods() *plugin.TValue[[]interface{}] {
	return &c.EmailMethods
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetFido2Methods() *plugin.TValue[[]interface{}] {
	return &c.Fido2Methods
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetSoftwareMethods() *plugin.TValue[[]interface{}] {
	return &c.SoftwareMethods
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetMicrosoftAuthenticator() *plugin.TValue[[]interface{}] {
	return &c.MicrosoftAuthenticator
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetPasswordMethods() *plugin.TValue[[]interface{}] {
	return &c.PasswordMethods
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetTemporaryAccessPassMethods() *plugin.TValue[[]interface{}] {
	return &c.TemporaryAccessPassMethods
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetWindowsHelloMethods() *plugin.TValue[[]interface{}] {
	return &c.WindowsHelloMethods
}

func (c *mqlMicrosoftUserAuthenticationMethods) GetRegistrationDetails() *plugin.TValue[*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails] {
	return plugin.GetOrCompute[*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails](&c.RegistrationDetails, func() (*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.user.authenticationMethods", c.__id, "registrationDetails")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails), nil
			}
		}

		return c.registrationDetails()
	})
}

// mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails for the microsoft.user.authenticationMethods.userRegistrationDetails resource
type mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetailsInternal it will be used here
	Id plugin.TValue[string]
	IsAdmin plugin.TValue[bool]
	IsMfaCapable plugin.TValue[bool]
	IsMfaRegistered plugin.TValue[bool]
	IsPasswordlessCapable plugin.TValue[bool]
	IsSsprCapable plugin.TValue[bool]
	IsSsprEnabled plugin.TValue[bool]
	IsSsprRegistered plugin.TValue[bool]
	IsSystemPreferredAuthenticationMethodEnabled plugin.TValue[bool]
	LastUpdatedDateTime plugin.TValue[*time.Time]
	MethodsRegistered plugin.TValue[[]interface{}]
	SystemPreferredAuthenticationMethods plugin.TValue[[]interface{}]
	UserDisplayName plugin.TValue[string]
	UserPreferredMethodForSecondaryAuthentication plugin.TValue[string]
	UserPrincipalName plugin.TValue[string]
	UserType plugin.TValue[string]
}

// createMicrosoftUserAuthenticationMethodsUserRegistrationDetails creates a new instance of this resource
func createMicrosoftUserAuthenticationMethodsUserRegistrationDetails(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.user.authenticationMethods.userRegistrationDetails", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) MqlName() string {
	return "microsoft.user.authenticationMethods.userRegistrationDetails"
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetIsAdmin() *plugin.TValue[bool] {
	return &c.IsAdmin
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetIsMfaCapable() *plugin.TValue[bool] {
	return &c.IsMfaCapable
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetIsMfaRegistered() *plugin.TValue[bool] {
	return &c.IsMfaRegistered
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetIsPasswordlessCapable() *plugin.TValue[bool] {
	return &c.IsPasswordlessCapable
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetIsSsprCapable() *plugin.TValue[bool] {
	return &c.IsSsprCapable
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetIsSsprEnabled() *plugin.TValue[bool] {
	return &c.IsSsprEnabled
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetIsSsprRegistered() *plugin.TValue[bool] {
	return &c.IsSsprRegistered
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetIsSystemPreferredAuthenticationMethodEnabled() *plugin.TValue[bool] {
	return &c.IsSystemPreferredAuthenticationMethodEnabled
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetLastUpdatedDateTime() *plugin.TValue[*time.Time] {
	return &c.LastUpdatedDateTime
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetMethodsRegistered() *plugin.TValue[[]interface{}] {
	return &c.MethodsRegistered
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetSystemPreferredAuthenticationMethods() *plugin.TValue[[]interface{}] {
	return &c.SystemPreferredAuthenticationMethods
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetUserDisplayName() *plugin.TValue[string] {
	return &c.UserDisplayName
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetUserPreferredMethodForSecondaryAuthentication() *plugin.TValue[string] {
	return &c.UserPreferredMethodForSecondaryAuthentication
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetUserPrincipalName() *plugin.TValue[string] {
	return &c.UserPrincipalName
}

func (c *mqlMicrosoftUserAuthenticationMethodsUserRegistrationDetails) GetUserType() *plugin.TValue[string] {
	return &c.UserType
}

// mqlMicrosoftGroup for the microsoft.group resource
type mqlMicrosoftGroup struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftGroupInternal it will be used here
	Id plugin.TValue[string]
	DisplayName plugin.TValue[string]
	SecurityEnabled plugin.TValue[bool]
	MailEnabled plugin.TValue[bool]
	MailNickname plugin.TValue[string]
	Mail plugin.TValue[string]
	Visibility plugin.TValue[string]
	Members plugin.TValue[[]interface{}]
	GroupTypes plugin.TValue[[]interface{}]
	MembershipRule plugin.TValue[string]
	MembershipRuleProcessingState plugin.TValue[string]
}

// createMicrosoftGroup creates a new instance of this resource
func createMicrosoftGroup(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftGroup{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.group", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftGroup) MqlName() string {
	return "microsoft.group"
}

func (c *mqlMicrosoftGroup) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftGroup) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftGroup) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftGroup) GetSecurityEnabled() *plugin.TValue[bool] {
	return &c.SecurityEnabled
}

func (c *mqlMicrosoftGroup) GetMailEnabled() *plugin.TValue[bool] {
	return &c.MailEnabled
}

func (c *mqlMicrosoftGroup) GetMailNickname() *plugin.TValue[string] {
	return &c.MailNickname
}

func (c *mqlMicrosoftGroup) GetMail() *plugin.TValue[string] {
	return &c.Mail
}

func (c *mqlMicrosoftGroup) GetVisibility() *plugin.TValue[string] {
	return &c.Visibility
}

func (c *mqlMicrosoftGroup) GetMembers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Members, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.group", c.__id, "members")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.members()
	})
}

func (c *mqlMicrosoftGroup) GetGroupTypes() *plugin.TValue[[]interface{}] {
	return &c.GroupTypes
}

func (c *mqlMicrosoftGroup) GetMembershipRule() *plugin.TValue[string] {
	return &c.MembershipRule
}

func (c *mqlMicrosoftGroup) GetMembershipRuleProcessingState() *plugin.TValue[string] {
	return &c.MembershipRuleProcessingState
}

// mqlMicrosoftDevices for the microsoft.devices resource
type mqlMicrosoftDevices struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDevicesInternal it will be used here
	Filter plugin.TValue[string]
	Search plugin.TValue[string]
	List plugin.TValue[[]interface{}]
}

// createMicrosoftDevices creates a new instance of this resource
func createMicrosoftDevices(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDevices{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.devices", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDevices) MqlName() string {
	return "microsoft.devices"
}

func (c *mqlMicrosoftDevices) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDevices) GetFilter() *plugin.TValue[string] {
	return &c.Filter
}

func (c *mqlMicrosoftDevices) GetSearch() *plugin.TValue[string] {
	return &c.Search
}

func (c *mqlMicrosoftDevices) GetList() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.List, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.devices", c.__id, "list")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.list()
	})
}

// mqlMicrosoftDevice for the microsoft.device resource
type mqlMicrosoftDevice struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDeviceInternal it will be used here
	Id plugin.TValue[string]
	DisplayName plugin.TValue[string]
	DeviceId plugin.TValue[string]
	DeviceCategory plugin.TValue[string]
	EnrollmentProfileName plugin.TValue[string]
	EnrollmentType plugin.TValue[string]
	IsCompliant plugin.TValue[bool]
	IsManaged plugin.TValue[bool]
	Manufacturer plugin.TValue[string]
	IsRooted plugin.TValue[bool]
	MdmAppId plugin.TValue[string]
	Model plugin.TValue[string]
	OperatingSystem plugin.TValue[string]
	OperatingSystemVersion plugin.TValue[string]
	PhysicalIds plugin.TValue[[]interface{}]
	RegistrationDateTime plugin.TValue[*time.Time]
	SystemLabels plugin.TValue[[]interface{}]
	TrustType plugin.TValue[string]
}

// createMicrosoftDevice creates a new instance of this resource
func createMicrosoftDevice(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDevice{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.device", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDevice) MqlName() string {
	return "microsoft.device"
}

func (c *mqlMicrosoftDevice) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDevice) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftDevice) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftDevice) GetDeviceId() *plugin.TValue[string] {
	return &c.DeviceId
}

func (c *mqlMicrosoftDevice) GetDeviceCategory() *plugin.TValue[string] {
	return &c.DeviceCategory
}

func (c *mqlMicrosoftDevice) GetEnrollmentProfileName() *plugin.TValue[string] {
	return &c.EnrollmentProfileName
}

func (c *mqlMicrosoftDevice) GetEnrollmentType() *plugin.TValue[string] {
	return &c.EnrollmentType
}

func (c *mqlMicrosoftDevice) GetIsCompliant() *plugin.TValue[bool] {
	return &c.IsCompliant
}

func (c *mqlMicrosoftDevice) GetIsManaged() *plugin.TValue[bool] {
	return &c.IsManaged
}

func (c *mqlMicrosoftDevice) GetManufacturer() *plugin.TValue[string] {
	return &c.Manufacturer
}

func (c *mqlMicrosoftDevice) GetIsRooted() *plugin.TValue[bool] {
	return &c.IsRooted
}

func (c *mqlMicrosoftDevice) GetMdmAppId() *plugin.TValue[string] {
	return &c.MdmAppId
}

func (c *mqlMicrosoftDevice) GetModel() *plugin.TValue[string] {
	return &c.Model
}

func (c *mqlMicrosoftDevice) GetOperatingSystem() *plugin.TValue[string] {
	return &c.OperatingSystem
}

func (c *mqlMicrosoftDevice) GetOperatingSystemVersion() *plugin.TValue[string] {
	return &c.OperatingSystemVersion
}

func (c *mqlMicrosoftDevice) GetPhysicalIds() *plugin.TValue[[]interface{}] {
	return &c.PhysicalIds
}

func (c *mqlMicrosoftDevice) GetRegistrationDateTime() *plugin.TValue[*time.Time] {
	return &c.RegistrationDateTime
}

func (c *mqlMicrosoftDevice) GetSystemLabels() *plugin.TValue[[]interface{}] {
	return &c.SystemLabels
}

func (c *mqlMicrosoftDevice) GetTrustType() *plugin.TValue[string] {
	return &c.TrustType
}

// mqlMicrosoftDomain for the microsoft.domain resource
type mqlMicrosoftDomain struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDomainInternal it will be used here
	Id plugin.TValue[string]
	AuthenticationType plugin.TValue[string]
	AvailabilityStatus plugin.TValue[string]
	IsAdminManaged plugin.TValue[bool]
	IsDefault plugin.TValue[bool]
	IsInitial plugin.TValue[bool]
	IsRoot plugin.TValue[bool]
	IsVerified plugin.TValue[bool]
	PasswordNotificationWindowInDays plugin.TValue[int64]
	PasswordValidityPeriodInDays plugin.TValue[int64]
	SupportedServices plugin.TValue[[]interface{}]
	ServiceConfigurationRecords plugin.TValue[[]interface{}]
}

// createMicrosoftDomain creates a new instance of this resource
func createMicrosoftDomain(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDomain{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.domain", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDomain) MqlName() string {
	return "microsoft.domain"
}

func (c *mqlMicrosoftDomain) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDomain) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftDomain) GetAuthenticationType() *plugin.TValue[string] {
	return &c.AuthenticationType
}

func (c *mqlMicrosoftDomain) GetAvailabilityStatus() *plugin.TValue[string] {
	return &c.AvailabilityStatus
}

func (c *mqlMicrosoftDomain) GetIsAdminManaged() *plugin.TValue[bool] {
	return &c.IsAdminManaged
}

func (c *mqlMicrosoftDomain) GetIsDefault() *plugin.TValue[bool] {
	return &c.IsDefault
}

func (c *mqlMicrosoftDomain) GetIsInitial() *plugin.TValue[bool] {
	return &c.IsInitial
}

func (c *mqlMicrosoftDomain) GetIsRoot() *plugin.TValue[bool] {
	return &c.IsRoot
}

func (c *mqlMicrosoftDomain) GetIsVerified() *plugin.TValue[bool] {
	return &c.IsVerified
}

func (c *mqlMicrosoftDomain) GetPasswordNotificationWindowInDays() *plugin.TValue[int64] {
	return &c.PasswordNotificationWindowInDays
}

func (c *mqlMicrosoftDomain) GetPasswordValidityPeriodInDays() *plugin.TValue[int64] {
	return &c.PasswordValidityPeriodInDays
}

func (c *mqlMicrosoftDomain) GetSupportedServices() *plugin.TValue[[]interface{}] {
	return &c.SupportedServices
}

func (c *mqlMicrosoftDomain) GetServiceConfigurationRecords() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ServiceConfigurationRecords, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.domain", c.__id, "serviceConfigurationRecords")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.serviceConfigurationRecords()
	})
}

// mqlMicrosoftDomaindnsrecord for the microsoft.domaindnsrecord resource
type mqlMicrosoftDomaindnsrecord struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDomaindnsrecordInternal it will be used here
	Id plugin.TValue[string]
	IsOptional plugin.TValue[bool]
	Label plugin.TValue[string]
	RecordType plugin.TValue[string]
	SupportedService plugin.TValue[string]
	Ttl plugin.TValue[int64]
	Properties plugin.TValue[interface{}]
}

// createMicrosoftDomaindnsrecord creates a new instance of this resource
func createMicrosoftDomaindnsrecord(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDomaindnsrecord{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.domaindnsrecord", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDomaindnsrecord) MqlName() string {
	return "microsoft.domaindnsrecord"
}

func (c *mqlMicrosoftDomaindnsrecord) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDomaindnsrecord) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftDomaindnsrecord) GetIsOptional() *plugin.TValue[bool] {
	return &c.IsOptional
}

func (c *mqlMicrosoftDomaindnsrecord) GetLabel() *plugin.TValue[string] {
	return &c.Label
}

func (c *mqlMicrosoftDomaindnsrecord) GetRecordType() *plugin.TValue[string] {
	return &c.RecordType
}

func (c *mqlMicrosoftDomaindnsrecord) GetSupportedService() *plugin.TValue[string] {
	return &c.SupportedService
}

func (c *mqlMicrosoftDomaindnsrecord) GetTtl() *plugin.TValue[int64] {
	return &c.Ttl
}

func (c *mqlMicrosoftDomaindnsrecord) GetProperties() *plugin.TValue[interface{}] {
	return &c.Properties
}

// mqlMicrosoftApplication for the microsoft.application resource
type mqlMicrosoftApplication struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftApplicationInternal it will be used here
	Id plugin.TValue[string]
	AppId plugin.TValue[string]
	Name plugin.TValue[string]
	DisplayName plugin.TValue[string]
	Description plugin.TValue[string]
	Notes plugin.TValue[string]
	Tags plugin.TValue[[]interface{}]
	ApplicationTemplateId plugin.TValue[string]
	DisabledByMicrosoftStatus plugin.TValue[string]
	GroupMembershipClaims plugin.TValue[string]
	CreatedAt plugin.TValue[*time.Time]
	CreatedDateTime plugin.TValue[*time.Time]
	IdentifierUris plugin.TValue[[]interface{}]
	PublisherDomain plugin.TValue[string]
	SignInAudience plugin.TValue[string]
	Info plugin.TValue[interface{}]
	Api plugin.TValue[interface{}]
	Web plugin.TValue[interface{}]
	Spa plugin.TValue[interface{}]
	Secrets plugin.TValue[[]interface{}]
	Certificates plugin.TValue[[]interface{}]
	HasExpiredCredentials plugin.TValue[bool]
	Owners plugin.TValue[[]interface{}]
	ServicePrincipal plugin.TValue[*mqlMicrosoftServiceprincipal]
	IsDeviceOnlyAuthSupported plugin.TValue[bool]
	IsFallbackPublicClient plugin.TValue[bool]
	NativeAuthenticationApisEnabled plugin.TValue[string]
	ServiceManagementReference plugin.TValue[string]
	TokenEncryptionKeyId plugin.TValue[string]
	SamlMetadataUrl plugin.TValue[string]
	DefaultRedirectUri plugin.TValue[string]
	Certification plugin.TValue[interface{}]
	OptionalClaims plugin.TValue[interface{}]
	ServicePrincipalLockConfiguration plugin.TValue[interface{}]
	RequestSignatureVerification plugin.TValue[interface{}]
	ParentalControlSettings plugin.TValue[interface{}]
	PublicClient plugin.TValue[interface{}]
	AppRoles plugin.TValue[[]interface{}]
}

// createMicrosoftApplication creates a new instance of this resource
func createMicrosoftApplication(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftApplication{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.application", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftApplication) MqlName() string {
	return "microsoft.application"
}

func (c *mqlMicrosoftApplication) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftApplication) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftApplication) GetAppId() *plugin.TValue[string] {
	return &c.AppId
}

func (c *mqlMicrosoftApplication) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftApplication) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftApplication) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftApplication) GetNotes() *plugin.TValue[string] {
	return &c.Notes
}

func (c *mqlMicrosoftApplication) GetTags() *plugin.TValue[[]interface{}] {
	return &c.Tags
}

func (c *mqlMicrosoftApplication) GetApplicationTemplateId() *plugin.TValue[string] {
	return &c.ApplicationTemplateId
}

func (c *mqlMicrosoftApplication) GetDisabledByMicrosoftStatus() *plugin.TValue[string] {
	return &c.DisabledByMicrosoftStatus
}

func (c *mqlMicrosoftApplication) GetGroupMembershipClaims() *plugin.TValue[string] {
	return &c.GroupMembershipClaims
}

func (c *mqlMicrosoftApplication) GetCreatedAt() *plugin.TValue[*time.Time] {
	return &c.CreatedAt
}

func (c *mqlMicrosoftApplication) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftApplication) GetIdentifierUris() *plugin.TValue[[]interface{}] {
	return &c.IdentifierUris
}

func (c *mqlMicrosoftApplication) GetPublisherDomain() *plugin.TValue[string] {
	return &c.PublisherDomain
}

func (c *mqlMicrosoftApplication) GetSignInAudience() *plugin.TValue[string] {
	return &c.SignInAudience
}

func (c *mqlMicrosoftApplication) GetInfo() *plugin.TValue[interface{}] {
	return &c.Info
}

func (c *mqlMicrosoftApplication) GetApi() *plugin.TValue[interface{}] {
	return &c.Api
}

func (c *mqlMicrosoftApplication) GetWeb() *plugin.TValue[interface{}] {
	return &c.Web
}

func (c *mqlMicrosoftApplication) GetSpa() *plugin.TValue[interface{}] {
	return &c.Spa
}

func (c *mqlMicrosoftApplication) GetSecrets() *plugin.TValue[[]interface{}] {
	return &c.Secrets
}

func (c *mqlMicrosoftApplication) GetCertificates() *plugin.TValue[[]interface{}] {
	return &c.Certificates
}

func (c *mqlMicrosoftApplication) GetHasExpiredCredentials() *plugin.TValue[bool] {
	return plugin.GetOrCompute[bool](&c.HasExpiredCredentials, func() (bool, error) {
		return c.hasExpiredCredentials()
	})
}

func (c *mqlMicrosoftApplication) GetOwners() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Owners, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.application", c.__id, "owners")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.owners()
	})
}

func (c *mqlMicrosoftApplication) GetServicePrincipal() *plugin.TValue[*mqlMicrosoftServiceprincipal] {
	return plugin.GetOrCompute[*mqlMicrosoftServiceprincipal](&c.ServicePrincipal, func() (*mqlMicrosoftServiceprincipal, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.application", c.__id, "servicePrincipal")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftServiceprincipal), nil
			}
		}

		return c.servicePrincipal()
	})
}

func (c *mqlMicrosoftApplication) GetIsDeviceOnlyAuthSupported() *plugin.TValue[bool] {
	return &c.IsDeviceOnlyAuthSupported
}

func (c *mqlMicrosoftApplication) GetIsFallbackPublicClient() *plugin.TValue[bool] {
	return &c.IsFallbackPublicClient
}

func (c *mqlMicrosoftApplication) GetNativeAuthenticationApisEnabled() *plugin.TValue[string] {
	return &c.NativeAuthenticationApisEnabled
}

func (c *mqlMicrosoftApplication) GetServiceManagementReference() *plugin.TValue[string] {
	return &c.ServiceManagementReference
}

func (c *mqlMicrosoftApplication) GetTokenEncryptionKeyId() *plugin.TValue[string] {
	return &c.TokenEncryptionKeyId
}

func (c *mqlMicrosoftApplication) GetSamlMetadataUrl() *plugin.TValue[string] {
	return &c.SamlMetadataUrl
}

func (c *mqlMicrosoftApplication) GetDefaultRedirectUri() *plugin.TValue[string] {
	return &c.DefaultRedirectUri
}

func (c *mqlMicrosoftApplication) GetCertification() *plugin.TValue[interface{}] {
	return &c.Certification
}

func (c *mqlMicrosoftApplication) GetOptionalClaims() *plugin.TValue[interface{}] {
	return &c.OptionalClaims
}

func (c *mqlMicrosoftApplication) GetServicePrincipalLockConfiguration() *plugin.TValue[interface{}] {
	return &c.ServicePrincipalLockConfiguration
}

func (c *mqlMicrosoftApplication) GetRequestSignatureVerification() *plugin.TValue[interface{}] {
	return &c.RequestSignatureVerification
}

func (c *mqlMicrosoftApplication) GetParentalControlSettings() *plugin.TValue[interface{}] {
	return &c.ParentalControlSettings
}

func (c *mqlMicrosoftApplication) GetPublicClient() *plugin.TValue[interface{}] {
	return &c.PublicClient
}

func (c *mqlMicrosoftApplication) GetAppRoles() *plugin.TValue[[]interface{}] {
	return &c.AppRoles
}

// mqlMicrosoftApplicationRole for the microsoft.application.role resource
type mqlMicrosoftApplicationRole struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftApplicationRoleInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	Description plugin.TValue[string]
	Value plugin.TValue[string]
	AllowedMemberTypes plugin.TValue[[]interface{}]
	IsEnabled plugin.TValue[bool]
}

// createMicrosoftApplicationRole creates a new instance of this resource
func createMicrosoftApplicationRole(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftApplicationRole{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.application.role", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftApplicationRole) MqlName() string {
	return "microsoft.application.role"
}

func (c *mqlMicrosoftApplicationRole) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftApplicationRole) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftApplicationRole) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftApplicationRole) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftApplicationRole) GetValue() *plugin.TValue[string] {
	return &c.Value
}

func (c *mqlMicrosoftApplicationRole) GetAllowedMemberTypes() *plugin.TValue[[]interface{}] {
	return &c.AllowedMemberTypes
}

func (c *mqlMicrosoftApplicationRole) GetIsEnabled() *plugin.TValue[bool] {
	return &c.IsEnabled
}

// mqlMicrosoftKeyCredential for the microsoft.keyCredential resource
type mqlMicrosoftKeyCredential struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftKeyCredentialInternal it will be used here
	KeyId plugin.TValue[string]
	Description plugin.TValue[string]
	Thumbprint plugin.TValue[string]
	Type plugin.TValue[string]
	Usage plugin.TValue[string]
	Expires plugin.TValue[*time.Time]
	Expired plugin.TValue[bool]
}

// createMicrosoftKeyCredential creates a new instance of this resource
func createMicrosoftKeyCredential(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftKeyCredential{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.keyCredential", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftKeyCredential) MqlName() string {
	return "microsoft.keyCredential"
}

func (c *mqlMicrosoftKeyCredential) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftKeyCredential) GetKeyId() *plugin.TValue[string] {
	return &c.KeyId
}

func (c *mqlMicrosoftKeyCredential) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftKeyCredential) GetThumbprint() *plugin.TValue[string] {
	return &c.Thumbprint
}

func (c *mqlMicrosoftKeyCredential) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlMicrosoftKeyCredential) GetUsage() *plugin.TValue[string] {
	return &c.Usage
}

func (c *mqlMicrosoftKeyCredential) GetExpires() *plugin.TValue[*time.Time] {
	return &c.Expires
}

func (c *mqlMicrosoftKeyCredential) GetExpired() *plugin.TValue[bool] {
	return &c.Expired
}

// mqlMicrosoftPasswordCredential for the microsoft.passwordCredential resource
type mqlMicrosoftPasswordCredential struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftPasswordCredentialInternal it will be used here
	KeyId plugin.TValue[string]
	Description plugin.TValue[string]
	Hint plugin.TValue[string]
	Expires plugin.TValue[*time.Time]
	Expired plugin.TValue[bool]
}

// createMicrosoftPasswordCredential creates a new instance of this resource
func createMicrosoftPasswordCredential(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftPasswordCredential{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.passwordCredential", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftPasswordCredential) MqlName() string {
	return "microsoft.passwordCredential"
}

func (c *mqlMicrosoftPasswordCredential) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftPasswordCredential) GetKeyId() *plugin.TValue[string] {
	return &c.KeyId
}

func (c *mqlMicrosoftPasswordCredential) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftPasswordCredential) GetHint() *plugin.TValue[string] {
	return &c.Hint
}

func (c *mqlMicrosoftPasswordCredential) GetExpires() *plugin.TValue[*time.Time] {
	return &c.Expires
}

func (c *mqlMicrosoftPasswordCredential) GetExpired() *plugin.TValue[bool] {
	return &c.Expired
}

// mqlMicrosoftServiceprincipal for the microsoft.serviceprincipal resource
type mqlMicrosoftServiceprincipal struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftServiceprincipalInternal it will be used here
	Id plugin.TValue[string]
	Type plugin.TValue[string]
	Name plugin.TValue[string]
	AppId plugin.TValue[string]
	AppOwnerOrganizationId plugin.TValue[string]
	Description plugin.TValue[string]
	Tags plugin.TValue[[]interface{}]
	Enabled plugin.TValue[bool]
	HomepageUrl plugin.TValue[string]
	TermsOfServiceUrl plugin.TValue[string]
	ReplyUrls plugin.TValue[[]interface{}]
	AssignmentRequired plugin.TValue[bool]
	VisibleToUsers plugin.TValue[bool]
	Notes plugin.TValue[string]
	Assignments plugin.TValue[[]interface{}]
	ApplicationTemplateId plugin.TValue[string]
	VerifiedPublisher plugin.TValue[interface{}]
	LoginUrl plugin.TValue[string]
	LogoutUrl plugin.TValue[string]
	ServicePrincipalNames plugin.TValue[[]interface{}]
	SignInAudience plugin.TValue[string]
	PreferredSingleSignOnMode plugin.TValue[string]
	NotificationEmailAddresses plugin.TValue[[]interface{}]
	AppRoleAssignmentRequired plugin.TValue[bool]
	AccountEnabled plugin.TValue[bool]
	IsFirstParty plugin.TValue[bool]
	AppRoles plugin.TValue[[]interface{}]
	Permissions plugin.TValue[[]interface{}]
}

// createMicrosoftServiceprincipal creates a new instance of this resource
func createMicrosoftServiceprincipal(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftServiceprincipal{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.serviceprincipal", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftServiceprincipal) MqlName() string {
	return "microsoft.serviceprincipal"
}

func (c *mqlMicrosoftServiceprincipal) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftServiceprincipal) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftServiceprincipal) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlMicrosoftServiceprincipal) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftServiceprincipal) GetAppId() *plugin.TValue[string] {
	return &c.AppId
}

func (c *mqlMicrosoftServiceprincipal) GetAppOwnerOrganizationId() *plugin.TValue[string] {
	return &c.AppOwnerOrganizationId
}

func (c *mqlMicrosoftServiceprincipal) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftServiceprincipal) GetTags() *plugin.TValue[[]interface{}] {
	return &c.Tags
}

func (c *mqlMicrosoftServiceprincipal) GetEnabled() *plugin.TValue[bool] {
	return &c.Enabled
}

func (c *mqlMicrosoftServiceprincipal) GetHomepageUrl() *plugin.TValue[string] {
	return &c.HomepageUrl
}

func (c *mqlMicrosoftServiceprincipal) GetTermsOfServiceUrl() *plugin.TValue[string] {
	return &c.TermsOfServiceUrl
}

func (c *mqlMicrosoftServiceprincipal) GetReplyUrls() *plugin.TValue[[]interface{}] {
	return &c.ReplyUrls
}

func (c *mqlMicrosoftServiceprincipal) GetAssignmentRequired() *plugin.TValue[bool] {
	return &c.AssignmentRequired
}

func (c *mqlMicrosoftServiceprincipal) GetVisibleToUsers() *plugin.TValue[bool] {
	return &c.VisibleToUsers
}

func (c *mqlMicrosoftServiceprincipal) GetNotes() *plugin.TValue[string] {
	return &c.Notes
}

func (c *mqlMicrosoftServiceprincipal) GetAssignments() *plugin.TValue[[]interface{}] {
	return &c.Assignments
}

func (c *mqlMicrosoftServiceprincipal) GetApplicationTemplateId() *plugin.TValue[string] {
	return &c.ApplicationTemplateId
}

func (c *mqlMicrosoftServiceprincipal) GetVerifiedPublisher() *plugin.TValue[interface{}] {
	return &c.VerifiedPublisher
}

func (c *mqlMicrosoftServiceprincipal) GetLoginUrl() *plugin.TValue[string] {
	return &c.LoginUrl
}

func (c *mqlMicrosoftServiceprincipal) GetLogoutUrl() *plugin.TValue[string] {
	return &c.LogoutUrl
}

func (c *mqlMicrosoftServiceprincipal) GetServicePrincipalNames() *plugin.TValue[[]interface{}] {
	return &c.ServicePrincipalNames
}

func (c *mqlMicrosoftServiceprincipal) GetSignInAudience() *plugin.TValue[string] {
	return &c.SignInAudience
}

func (c *mqlMicrosoftServiceprincipal) GetPreferredSingleSignOnMode() *plugin.TValue[string] {
	return &c.PreferredSingleSignOnMode
}

func (c *mqlMicrosoftServiceprincipal) GetNotificationEmailAddresses() *plugin.TValue[[]interface{}] {
	return &c.NotificationEmailAddresses
}

func (c *mqlMicrosoftServiceprincipal) GetAppRoleAssignmentRequired() *plugin.TValue[bool] {
	return &c.AppRoleAssignmentRequired
}

func (c *mqlMicrosoftServiceprincipal) GetAccountEnabled() *plugin.TValue[bool] {
	return &c.AccountEnabled
}

func (c *mqlMicrosoftServiceprincipal) GetIsFirstParty() *plugin.TValue[bool] {
	return plugin.GetOrCompute[bool](&c.IsFirstParty, func() (bool, error) {
		return c.isFirstParty()
	})
}

func (c *mqlMicrosoftServiceprincipal) GetAppRoles() *plugin.TValue[[]interface{}] {
	return &c.AppRoles
}

func (c *mqlMicrosoftServiceprincipal) GetPermissions() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Permissions, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.serviceprincipal", c.__id, "permissions")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.permissions()
	})
}

// mqlMicrosoftServiceprincipalAssignment for the microsoft.serviceprincipal.assignment resource
type mqlMicrosoftServiceprincipalAssignment struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftServiceprincipalAssignmentInternal it will be used here
	Id plugin.TValue[string]
	DisplayName plugin.TValue[string]
	Type plugin.TValue[string]
}

// createMicrosoftServiceprincipalAssignment creates a new instance of this resource
func createMicrosoftServiceprincipalAssignment(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftServiceprincipalAssignment{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.serviceprincipal.assignment", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftServiceprincipalAssignment) MqlName() string {
	return "microsoft.serviceprincipal.assignment"
}

func (c *mqlMicrosoftServiceprincipalAssignment) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftServiceprincipalAssignment) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftServiceprincipalAssignment) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftServiceprincipalAssignment) GetType() *plugin.TValue[string] {
	return &c.Type
}

// mqlMicrosoftApplicationPermission for the microsoft.application.permission resource
type mqlMicrosoftApplicationPermission struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftApplicationPermissionInternal it will be used here
	AppId plugin.TValue[string]
	AppName plugin.TValue[string]
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	Description plugin.TValue[string]
	Type plugin.TValue[string]
	Status plugin.TValue[string]
}

// createMicrosoftApplicationPermission creates a new instance of this resource
func createMicrosoftApplicationPermission(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftApplicationPermission{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.application.permission", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftApplicationPermission) MqlName() string {
	return "microsoft.application.permission"
}

func (c *mqlMicrosoftApplicationPermission) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftApplicationPermission) GetAppId() *plugin.TValue[string] {
	return &c.AppId
}

func (c *mqlMicrosoftApplicationPermission) GetAppName() *plugin.TValue[string] {
	return &c.AppName
}

func (c *mqlMicrosoftApplicationPermission) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftApplicationPermission) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftApplicationPermission) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftApplicationPermission) GetType() *plugin.TValue[string] {
	return &c.Type
}

func (c *mqlMicrosoftApplicationPermission) GetStatus() *plugin.TValue[string] {
	return &c.Status
}

// mqlMicrosoftSecurity for the microsoft.security resource
type mqlMicrosoftSecurity struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftSecurityInternal it will be used here
	SecureScores plugin.TValue[[]interface{}]
	LatestSecureScores plugin.TValue[*mqlMicrosoftSecuritySecurityscore]
	RiskyUsers plugin.TValue[[]interface{}]
}

// createMicrosoftSecurity creates a new instance of this resource
func createMicrosoftSecurity(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftSecurity{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.security", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftSecurity) MqlName() string {
	return "microsoft.security"
}

func (c *mqlMicrosoftSecurity) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftSecurity) GetSecureScores() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.SecureScores, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.security", c.__id, "secureScores")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.secureScores()
	})
}

func (c *mqlMicrosoftSecurity) GetLatestSecureScores() *plugin.TValue[*mqlMicrosoftSecuritySecurityscore] {
	return plugin.GetOrCompute[*mqlMicrosoftSecuritySecurityscore](&c.LatestSecureScores, func() (*mqlMicrosoftSecuritySecurityscore, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.security", c.__id, "latestSecureScores")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftSecuritySecurityscore), nil
			}
		}

		return c.latestSecureScores()
	})
}

func (c *mqlMicrosoftSecurity) GetRiskyUsers() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.RiskyUsers, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.security", c.__id, "riskyUsers")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.riskyUsers()
	})
}

// mqlMicrosoftSecuritySecurityscore for the microsoft.security.securityscore resource
type mqlMicrosoftSecuritySecurityscore struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftSecuritySecurityscoreInternal it will be used here
	Id plugin.TValue[string]
	ActiveUserCount plugin.TValue[int64]
	AverageComparativeScores plugin.TValue[[]interface{}]
	AzureTenantId plugin.TValue[string]
	ControlScores plugin.TValue[[]interface{}]
	CreatedDateTime plugin.TValue[*time.Time]
	CurrentScore plugin.TValue[float64]
	EnabledServices plugin.TValue[[]interface{}]
	LicensedUserCount plugin.TValue[int64]
	MaxScore plugin.TValue[float64]
	VendorInformation plugin.TValue[interface{}]
}

// createMicrosoftSecuritySecurityscore creates a new instance of this resource
func createMicrosoftSecuritySecurityscore(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftSecuritySecurityscore{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.security.securityscore", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftSecuritySecurityscore) MqlName() string {
	return "microsoft.security.securityscore"
}

func (c *mqlMicrosoftSecuritySecurityscore) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftSecuritySecurityscore) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftSecuritySecurityscore) GetActiveUserCount() *plugin.TValue[int64] {
	return &c.ActiveUserCount
}

func (c *mqlMicrosoftSecuritySecurityscore) GetAverageComparativeScores() *plugin.TValue[[]interface{}] {
	return &c.AverageComparativeScores
}

func (c *mqlMicrosoftSecuritySecurityscore) GetAzureTenantId() *plugin.TValue[string] {
	return &c.AzureTenantId
}

func (c *mqlMicrosoftSecuritySecurityscore) GetControlScores() *plugin.TValue[[]interface{}] {
	return &c.ControlScores
}

func (c *mqlMicrosoftSecuritySecurityscore) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftSecuritySecurityscore) GetCurrentScore() *plugin.TValue[float64] {
	return &c.CurrentScore
}

func (c *mqlMicrosoftSecuritySecurityscore) GetEnabledServices() *plugin.TValue[[]interface{}] {
	return &c.EnabledServices
}

func (c *mqlMicrosoftSecuritySecurityscore) GetLicensedUserCount() *plugin.TValue[int64] {
	return &c.LicensedUserCount
}

func (c *mqlMicrosoftSecuritySecurityscore) GetMaxScore() *plugin.TValue[float64] {
	return &c.MaxScore
}

func (c *mqlMicrosoftSecuritySecurityscore) GetVendorInformation() *plugin.TValue[interface{}] {
	return &c.VendorInformation
}

// mqlMicrosoftSecurityRiskyUser for the microsoft.security.riskyUser resource
type mqlMicrosoftSecurityRiskyUser struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftSecurityRiskyUserInternal it will be used here
	Id plugin.TValue[string]
	Name plugin.TValue[string]
	PrincipalName plugin.TValue[string]
	User plugin.TValue[*mqlMicrosoftUser]
	RiskDetail plugin.TValue[string]
	RiskLevel plugin.TValue[string]
	RiskState plugin.TValue[string]
	LastUpdatedAt plugin.TValue[*time.Time]
}

// createMicrosoftSecurityRiskyUser creates a new instance of this resource
func createMicrosoftSecurityRiskyUser(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftSecurityRiskyUser{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.security.riskyUser", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftSecurityRiskyUser) MqlName() string {
	return "microsoft.security.riskyUser"
}

func (c *mqlMicrosoftSecurityRiskyUser) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftSecurityRiskyUser) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftSecurityRiskyUser) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftSecurityRiskyUser) GetPrincipalName() *plugin.TValue[string] {
	return &c.PrincipalName
}

func (c *mqlMicrosoftSecurityRiskyUser) GetUser() *plugin.TValue[*mqlMicrosoftUser] {
	return plugin.GetOrCompute[*mqlMicrosoftUser](&c.User, func() (*mqlMicrosoftUser, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.security.riskyUser", c.__id, "user")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUser), nil
			}
		}

		return c.user()
	})
}

func (c *mqlMicrosoftSecurityRiskyUser) GetRiskDetail() *plugin.TValue[string] {
	return &c.RiskDetail
}

func (c *mqlMicrosoftSecurityRiskyUser) GetRiskLevel() *plugin.TValue[string] {
	return &c.RiskLevel
}

func (c *mqlMicrosoftSecurityRiskyUser) GetRiskState() *plugin.TValue[string] {
	return &c.RiskState
}

func (c *mqlMicrosoftSecurityRiskyUser) GetLastUpdatedAt() *plugin.TValue[*time.Time] {
	return &c.LastUpdatedAt
}

// mqlMicrosoftPolicies for the microsoft.policies resource
type mqlMicrosoftPolicies struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftPoliciesInternal it will be used here
	AuthorizationPolicy plugin.TValue[interface{}]
	IdentitySecurityDefaultsEnforcementPolicy plugin.TValue[interface{}]
	AdminConsentRequestPolicy plugin.TValue[*mqlMicrosoftAdminConsentRequestPolicy]
	PermissionGrantPolicies plugin.TValue[[]interface{}]
	ConsentPolicySettings plugin.TValue[interface{}]
	AuthenticationMethodsPolicy plugin.TValue[*mqlMicrosoftPoliciesAuthenticationMethodsPolicy]
}

// createMicrosoftPolicies creates a new instance of this resource
func createMicrosoftPolicies(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftPolicies{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.policies", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftPolicies) MqlName() string {
	return "microsoft.policies"
}

func (c *mqlMicrosoftPolicies) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftPolicies) GetAuthorizationPolicy() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.AuthorizationPolicy, func() (interface{}, error) {
		return c.authorizationPolicy()
	})
}

func (c *mqlMicrosoftPolicies) GetIdentitySecurityDefaultsEnforcementPolicy() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.IdentitySecurityDefaultsEnforcementPolicy, func() (interface{}, error) {
		return c.identitySecurityDefaultsEnforcementPolicy()
	})
}

func (c *mqlMicrosoftPolicies) GetAdminConsentRequestPolicy() *plugin.TValue[*mqlMicrosoftAdminConsentRequestPolicy] {
	return plugin.GetOrCompute[*mqlMicrosoftAdminConsentRequestPolicy](&c.AdminConsentRequestPolicy, func() (*mqlMicrosoftAdminConsentRequestPolicy, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.policies", c.__id, "adminConsentRequestPolicy")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftAdminConsentRequestPolicy), nil
			}
		}

		return c.adminConsentRequestPolicy()
	})
}

func (c *mqlMicrosoftPolicies) GetPermissionGrantPolicies() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.PermissionGrantPolicies, func() ([]interface{}, error) {
		return c.permissionGrantPolicies()
	})
}

func (c *mqlMicrosoftPolicies) GetConsentPolicySettings() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.ConsentPolicySettings, func() (interface{}, error) {
		return c.consentPolicySettings()
	})
}

func (c *mqlMicrosoftPolicies) GetAuthenticationMethodsPolicy() *plugin.TValue[*mqlMicrosoftPoliciesAuthenticationMethodsPolicy] {
	return plugin.GetOrCompute[*mqlMicrosoftPoliciesAuthenticationMethodsPolicy](&c.AuthenticationMethodsPolicy, func() (*mqlMicrosoftPoliciesAuthenticationMethodsPolicy, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.policies", c.__id, "authenticationMethodsPolicy")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftPoliciesAuthenticationMethodsPolicy), nil
			}
		}

		return c.authenticationMethodsPolicy()
	})
}

// mqlMicrosoftAdminConsentRequestPolicy for the microsoft.adminConsentRequestPolicy resource
type mqlMicrosoftAdminConsentRequestPolicy struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftAdminConsentRequestPolicyInternal it will be used here
	IsEnabled plugin.TValue[bool]
	NotifyReviewers plugin.TValue[bool]
	RemindersEnabled plugin.TValue[bool]
	RequestDurationInDays plugin.TValue[int64]
	Reviewers plugin.TValue[[]interface{}]
	Version plugin.TValue[int64]
}

// createMicrosoftAdminConsentRequestPolicy creates a new instance of this resource
func createMicrosoftAdminConsentRequestPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftAdminConsentRequestPolicy{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.adminConsentRequestPolicy", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftAdminConsentRequestPolicy) MqlName() string {
	return "microsoft.adminConsentRequestPolicy"
}

func (c *mqlMicrosoftAdminConsentRequestPolicy) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftAdminConsentRequestPolicy) GetIsEnabled() *plugin.TValue[bool] {
	return &c.IsEnabled
}

func (c *mqlMicrosoftAdminConsentRequestPolicy) GetNotifyReviewers() *plugin.TValue[bool] {
	return &c.NotifyReviewers
}

func (c *mqlMicrosoftAdminConsentRequestPolicy) GetRemindersEnabled() *plugin.TValue[bool] {
	return &c.RemindersEnabled
}

func (c *mqlMicrosoftAdminConsentRequestPolicy) GetRequestDurationInDays() *plugin.TValue[int64] {
	return &c.RequestDurationInDays
}

func (c *mqlMicrosoftAdminConsentRequestPolicy) GetReviewers() *plugin.TValue[[]interface{}] {
	return &c.Reviewers
}

func (c *mqlMicrosoftAdminConsentRequestPolicy) GetVersion() *plugin.TValue[int64] {
	return &c.Version
}

// mqlMicrosoftGraphAccessReviewReviewerScope for the microsoft.graph.accessReviewReviewerScope resource
type mqlMicrosoftGraphAccessReviewReviewerScope struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftGraphAccessReviewReviewerScopeInternal it will be used here
	Query plugin.TValue[string]
	QueryRoot plugin.TValue[string]
	QueryType plugin.TValue[string]
}

// createMicrosoftGraphAccessReviewReviewerScope creates a new instance of this resource
func createMicrosoftGraphAccessReviewReviewerScope(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftGraphAccessReviewReviewerScope{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.graph.accessReviewReviewerScope", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftGraphAccessReviewReviewerScope) MqlName() string {
	return "microsoft.graph.accessReviewReviewerScope"
}

func (c *mqlMicrosoftGraphAccessReviewReviewerScope) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftGraphAccessReviewReviewerScope) GetQuery() *plugin.TValue[string] {
	return &c.Query
}

func (c *mqlMicrosoftGraphAccessReviewReviewerScope) GetQueryRoot() *plugin.TValue[string] {
	return &c.QueryRoot
}

func (c *mqlMicrosoftGraphAccessReviewReviewerScope) GetQueryType() *plugin.TValue[string] {
	return &c.QueryType
}

// mqlMicrosoftPoliciesAuthenticationMethodsPolicy for the microsoft.policies.authenticationMethodsPolicy resource
type mqlMicrosoftPoliciesAuthenticationMethodsPolicy struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftPoliciesAuthenticationMethodsPolicyInternal it will be used here
	Id plugin.TValue[string]
	DisplayName plugin.TValue[string]
	Description plugin.TValue[string]
	LastModifiedDateTime plugin.TValue[*time.Time]
	PolicyVersion plugin.TValue[string]
	AuthenticationMethodConfigurations plugin.TValue[[]interface{}]
}

// createMicrosoftPoliciesAuthenticationMethodsPolicy creates a new instance of this resource
func createMicrosoftPoliciesAuthenticationMethodsPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftPoliciesAuthenticationMethodsPolicy{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.policies.authenticationMethodsPolicy", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodsPolicy) MqlName() string {
	return "microsoft.policies.authenticationMethodsPolicy"
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodsPolicy) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodsPolicy) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodsPolicy) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodsPolicy) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodsPolicy) GetLastModifiedDateTime() *plugin.TValue[*time.Time] {
	return &c.LastModifiedDateTime
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodsPolicy) GetPolicyVersion() *plugin.TValue[string] {
	return &c.PolicyVersion
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodsPolicy) GetAuthenticationMethodConfigurations() *plugin.TValue[[]interface{}] {
	return &c.AuthenticationMethodConfigurations
}

// mqlMicrosoftPoliciesAuthenticationMethodConfiguration for the microsoft.policies.authenticationMethodConfiguration resource
type mqlMicrosoftPoliciesAuthenticationMethodConfiguration struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftPoliciesAuthenticationMethodConfigurationInternal it will be used here
	Id plugin.TValue[string]
	State plugin.TValue[string]
	ExcludeTargets plugin.TValue[[]interface{}]
}

// createMicrosoftPoliciesAuthenticationMethodConfiguration creates a new instance of this resource
func createMicrosoftPoliciesAuthenticationMethodConfiguration(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftPoliciesAuthenticationMethodConfiguration{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.policies.authenticationMethodConfiguration", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodConfiguration) MqlName() string {
	return "microsoft.policies.authenticationMethodConfiguration"
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodConfiguration) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodConfiguration) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodConfiguration) GetState() *plugin.TValue[string] {
	return &c.State
}

func (c *mqlMicrosoftPoliciesAuthenticationMethodConfiguration) GetExcludeTargets() *plugin.TValue[[]interface{}] {
	return &c.ExcludeTargets
}

// mqlMicrosoftRoles for the microsoft.roles resource
type mqlMicrosoftRoles struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftRolesInternal it will be used here
	Filter plugin.TValue[string]
	Search plugin.TValue[string]
	List plugin.TValue[[]interface{}]
}

// createMicrosoftRoles creates a new instance of this resource
func createMicrosoftRoles(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftRoles{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.roles", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftRoles) MqlName() string {
	return "microsoft.roles"
}

func (c *mqlMicrosoftRoles) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftRoles) GetFilter() *plugin.TValue[string] {
	return &c.Filter
}

func (c *mqlMicrosoftRoles) GetSearch() *plugin.TValue[string] {
	return &c.Search
}

func (c *mqlMicrosoftRoles) GetList() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.List, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.roles", c.__id, "list")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.list()
	})
}

// mqlMicrosoftRolemanagement for the microsoft.rolemanagement resource
type mqlMicrosoftRolemanagement struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftRolemanagementInternal it will be used here
	RoleDefinitions plugin.TValue[*mqlMicrosoftRoles]
}

// createMicrosoftRolemanagement creates a new instance of this resource
func createMicrosoftRolemanagement(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftRolemanagement{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.rolemanagement", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftRolemanagement) MqlName() string {
	return "microsoft.rolemanagement"
}

func (c *mqlMicrosoftRolemanagement) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftRolemanagement) GetRoleDefinitions() *plugin.TValue[*mqlMicrosoftRoles] {
	return plugin.GetOrCompute[*mqlMicrosoftRoles](&c.RoleDefinitions, func() (*mqlMicrosoftRoles, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.rolemanagement", c.__id, "roleDefinitions")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftRoles), nil
			}
		}

		return c.roleDefinitions()
	})
}

// mqlMicrosoftRolemanagementRoledefinition for the microsoft.rolemanagement.roledefinition resource
type mqlMicrosoftRolemanagementRoledefinition struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftRolemanagementRoledefinitionInternal it will be used here
	Id plugin.TValue[string]
	Description plugin.TValue[string]
	DisplayName plugin.TValue[string]
	IsBuiltIn plugin.TValue[bool]
	IsEnabled plugin.TValue[bool]
	RolePermissions plugin.TValue[[]interface{}]
	TemplateId plugin.TValue[string]
	Version plugin.TValue[string]
	Assignments plugin.TValue[[]interface{}]
}

// createMicrosoftRolemanagementRoledefinition creates a new instance of this resource
func createMicrosoftRolemanagementRoledefinition(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftRolemanagementRoledefinition{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.rolemanagement.roledefinition", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftRolemanagementRoledefinition) MqlName() string {
	return "microsoft.rolemanagement.roledefinition"
}

func (c *mqlMicrosoftRolemanagementRoledefinition) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetIsBuiltIn() *plugin.TValue[bool] {
	return &c.IsBuiltIn
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetIsEnabled() *plugin.TValue[bool] {
	return &c.IsEnabled
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetRolePermissions() *plugin.TValue[[]interface{}] {
	return &c.RolePermissions
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetTemplateId() *plugin.TValue[string] {
	return &c.TemplateId
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetVersion() *plugin.TValue[string] {
	return &c.Version
}

func (c *mqlMicrosoftRolemanagementRoledefinition) GetAssignments() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Assignments, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.rolemanagement.roledefinition", c.__id, "assignments")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.assignments()
	})
}

// mqlMicrosoftRolemanagementRoleassignment for the microsoft.rolemanagement.roleassignment resource
type mqlMicrosoftRolemanagementRoleassignment struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftRolemanagementRoleassignmentInternal it will be used here
	Id plugin.TValue[string]
	RoleDefinitionId plugin.TValue[string]
	PrincipalId plugin.TValue[string]
	Principal plugin.TValue[interface{}]
}

// createMicrosoftRolemanagementRoleassignment creates a new instance of this resource
func createMicrosoftRolemanagementRoleassignment(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftRolemanagementRoleassignment{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.rolemanagement.roleassignment", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftRolemanagementRoleassignment) MqlName() string {
	return "microsoft.rolemanagement.roleassignment"
}

func (c *mqlMicrosoftRolemanagementRoleassignment) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftRolemanagementRoleassignment) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftRolemanagementRoleassignment) GetRoleDefinitionId() *plugin.TValue[string] {
	return &c.RoleDefinitionId
}

func (c *mqlMicrosoftRolemanagementRoleassignment) GetPrincipalId() *plugin.TValue[string] {
	return &c.PrincipalId
}

func (c *mqlMicrosoftRolemanagementRoleassignment) GetPrincipal() *plugin.TValue[interface{}] {
	return &c.Principal
}

// mqlMicrosoftDevicemanagement for the microsoft.devicemanagement resource
type mqlMicrosoftDevicemanagement struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDevicemanagementInternal it will be used here
	ManagedDevices plugin.TValue[[]interface{}]
	DeviceConfigurations plugin.TValue[[]interface{}]
	DeviceCompliancePolicies plugin.TValue[[]interface{}]
	DeviceEnrollmentConfigurations plugin.TValue[[]interface{}]
	Settings plugin.TValue[*mqlMicrosoftDevicemanagementSettings]
}

// createMicrosoftDevicemanagement creates a new instance of this resource
func createMicrosoftDevicemanagement(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDevicemanagement{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.devicemanagement", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDevicemanagement) MqlName() string {
	return "microsoft.devicemanagement"
}

func (c *mqlMicrosoftDevicemanagement) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDevicemanagement) GetManagedDevices() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ManagedDevices, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.devicemanagement", c.__id, "managedDevices")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.managedDevices()
	})
}

func (c *mqlMicrosoftDevicemanagement) GetDeviceConfigurations() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.DeviceConfigurations, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.devicemanagement", c.__id, "deviceConfigurations")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.deviceConfigurations()
	})
}

func (c *mqlMicrosoftDevicemanagement) GetDeviceCompliancePolicies() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.DeviceCompliancePolicies, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.devicemanagement", c.__id, "deviceCompliancePolicies")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.deviceCompliancePolicies()
	})
}

func (c *mqlMicrosoftDevicemanagement) GetDeviceEnrollmentConfigurations() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.DeviceEnrollmentConfigurations, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.devicemanagement", c.__id, "deviceEnrollmentConfigurations")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.deviceEnrollmentConfigurations()
	})
}

func (c *mqlMicrosoftDevicemanagement) GetSettings() *plugin.TValue[*mqlMicrosoftDevicemanagementSettings] {
	return plugin.GetOrCompute[*mqlMicrosoftDevicemanagementSettings](&c.Settings, func() (*mqlMicrosoftDevicemanagementSettings, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("microsoft.devicemanagement", c.__id, "settings")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftDevicemanagementSettings), nil
			}
		}

		return c.settings()
	})
}

// mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration for the microsoft.devicemanagement.deviceEnrollmentConfiguration resource
type mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDevicemanagementDeviceEnrollmentConfigurationInternal it will be used here
	Id plugin.TValue[string]
	DisplayName plugin.TValue[string]
	Description plugin.TValue[string]
	Priority plugin.TValue[int64]
	CreatedDateTime plugin.TValue[*time.Time]
	LastModifiedDateTime plugin.TValue[*time.Time]
	Version plugin.TValue[int64]
}

// createMicrosoftDevicemanagementDeviceEnrollmentConfiguration creates a new instance of this resource
func createMicrosoftDevicemanagementDeviceEnrollmentConfiguration(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.devicemanagement.deviceEnrollmentConfiguration", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) MqlName() string {
	return "microsoft.devicemanagement.deviceEnrollmentConfiguration"
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) GetPriority() *plugin.TValue[int64] {
	return &c.Priority
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) GetLastModifiedDateTime() *plugin.TValue[*time.Time] {
	return &c.LastModifiedDateTime
}

func (c *mqlMicrosoftDevicemanagementDeviceEnrollmentConfiguration) GetVersion() *plugin.TValue[int64] {
	return &c.Version
}

// mqlMicrosoftDevicemanagementManageddevice for the microsoft.devicemanagement.manageddevice resource
type mqlMicrosoftDevicemanagementManageddevice struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDevicemanagementManageddeviceInternal it will be used here
	Id plugin.TValue[string]
	UserId plugin.TValue[string]
	Name plugin.TValue[string]
	OperatingSystem plugin.TValue[string]
	JailBroken plugin.TValue[string]
	OsVersion plugin.TValue[string]
	EasActivated plugin.TValue[bool]
	EasDeviceId plugin.TValue[string]
	AzureADRegistered plugin.TValue[bool]
	EmailAddress plugin.TValue[string]
	AzureActiveDirectoryDeviceId plugin.TValue[string]
	DeviceCategoryDisplayName plugin.TValue[string]
	IsSupervised plugin.TValue[bool]
	IsEncrypted plugin.TValue[bool]
	UserPrincipalName plugin.TValue[string]
	Model plugin.TValue[string]
	Manufacturer plugin.TValue[string]
	Imei plugin.TValue[string]
	SerialNumber plugin.TValue[string]
	AndroidSecurityPatchLevel plugin.TValue[string]
	UserDisplayName plugin.TValue[string]
	WiFiMacAddress plugin.TValue[string]
	Meid plugin.TValue[string]
	Iccid plugin.TValue[string]
	Udid plugin.TValue[string]
	Notes plugin.TValue[string]
	EthernetMacAddress plugin.TValue[string]
	EnrollmentProfileName plugin.TValue[string]
	WindowsProtectionState plugin.TValue[interface{}]
}

// createMicrosoftDevicemanagementManageddevice creates a new instance of this resource
func createMicrosoftDevicemanagementManageddevice(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDevicemanagementManageddevice{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.devicemanagement.manageddevice", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDevicemanagementManageddevice) MqlName() string {
	return "microsoft.devicemanagement.manageddevice"
}

func (c *mqlMicrosoftDevicemanagementManageddevice) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetUserId() *plugin.TValue[string] {
	return &c.UserId
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetName() *plugin.TValue[string] {
	return &c.Name
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetOperatingSystem() *plugin.TValue[string] {
	return &c.OperatingSystem
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetJailBroken() *plugin.TValue[string] {
	return &c.JailBroken
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetOsVersion() *plugin.TValue[string] {
	return &c.OsVersion
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetEasActivated() *plugin.TValue[bool] {
	return &c.EasActivated
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetEasDeviceId() *plugin.TValue[string] {
	return &c.EasDeviceId
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetAzureADRegistered() *plugin.TValue[bool] {
	return &c.AzureADRegistered
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetEmailAddress() *plugin.TValue[string] {
	return &c.EmailAddress
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetAzureActiveDirectoryDeviceId() *plugin.TValue[string] {
	return &c.AzureActiveDirectoryDeviceId
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetDeviceCategoryDisplayName() *plugin.TValue[string] {
	return &c.DeviceCategoryDisplayName
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetIsSupervised() *plugin.TValue[bool] {
	return &c.IsSupervised
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetIsEncrypted() *plugin.TValue[bool] {
	return &c.IsEncrypted
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetUserPrincipalName() *plugin.TValue[string] {
	return &c.UserPrincipalName
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetModel() *plugin.TValue[string] {
	return &c.Model
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetManufacturer() *plugin.TValue[string] {
	return &c.Manufacturer
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetImei() *plugin.TValue[string] {
	return &c.Imei
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetSerialNumber() *plugin.TValue[string] {
	return &c.SerialNumber
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetAndroidSecurityPatchLevel() *plugin.TValue[string] {
	return &c.AndroidSecurityPatchLevel
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetUserDisplayName() *plugin.TValue[string] {
	return &c.UserDisplayName
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetWiFiMacAddress() *plugin.TValue[string] {
	return &c.WiFiMacAddress
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetMeid() *plugin.TValue[string] {
	return &c.Meid
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetIccid() *plugin.TValue[string] {
	return &c.Iccid
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetUdid() *plugin.TValue[string] {
	return &c.Udid
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetNotes() *plugin.TValue[string] {
	return &c.Notes
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetEthernetMacAddress() *plugin.TValue[string] {
	return &c.EthernetMacAddress
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetEnrollmentProfileName() *plugin.TValue[string] {
	return &c.EnrollmentProfileName
}

func (c *mqlMicrosoftDevicemanagementManageddevice) GetWindowsProtectionState() *plugin.TValue[interface{}] {
	return &c.WindowsProtectionState
}

// mqlMicrosoftDevicemanagementDeviceconfiguration for the microsoft.devicemanagement.deviceconfiguration resource
type mqlMicrosoftDevicemanagementDeviceconfiguration struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDevicemanagementDeviceconfigurationInternal it will be used here
	Id plugin.TValue[string]
	LastModifiedDateTime plugin.TValue[*time.Time]
	CreatedDateTime plugin.TValue[*time.Time]
	Description plugin.TValue[string]
	DisplayName plugin.TValue[string]
	Version plugin.TValue[int64]
	Properties plugin.TValue[interface{}]
}

// createMicrosoftDevicemanagementDeviceconfiguration creates a new instance of this resource
func createMicrosoftDevicemanagementDeviceconfiguration(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDevicemanagementDeviceconfiguration{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.devicemanagement.deviceconfiguration", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) MqlName() string {
	return "microsoft.devicemanagement.deviceconfiguration"
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) GetLastModifiedDateTime() *plugin.TValue[*time.Time] {
	return &c.LastModifiedDateTime
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) GetVersion() *plugin.TValue[int64] {
	return &c.Version
}

func (c *mqlMicrosoftDevicemanagementDeviceconfiguration) GetProperties() *plugin.TValue[interface{}] {
	return &c.Properties
}

// mqlMicrosoftDevicemanagementDevicecompliancepolicy for the microsoft.devicemanagement.devicecompliancepolicy resource
type mqlMicrosoftDevicemanagementDevicecompliancepolicy struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDevicemanagementDevicecompliancepolicyInternal it will be used here
	Id plugin.TValue[string]
	CreatedDateTime plugin.TValue[*time.Time]
	Description plugin.TValue[string]
	DisplayName plugin.TValue[string]
	LastModifiedDateTime plugin.TValue[*time.Time]
	Version plugin.TValue[int64]
	Assignments plugin.TValue[[]interface{}]
	Properties plugin.TValue[interface{}]
}

// createMicrosoftDevicemanagementDevicecompliancepolicy creates a new instance of this resource
func createMicrosoftDevicemanagementDevicecompliancepolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDevicemanagementDevicecompliancepolicy{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.devicemanagement.devicecompliancepolicy", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) MqlName() string {
	return "microsoft.devicemanagement.devicecompliancepolicy"
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) GetId() *plugin.TValue[string] {
	return &c.Id
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) GetCreatedDateTime() *plugin.TValue[*time.Time] {
	return &c.CreatedDateTime
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) GetDescription() *plugin.TValue[string] {
	return &c.Description
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) GetLastModifiedDateTime() *plugin.TValue[*time.Time] {
	return &c.LastModifiedDateTime
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) GetVersion() *plugin.TValue[int64] {
	return &c.Version
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) GetAssignments() *plugin.TValue[[]interface{}] {
	return &c.Assignments
}

func (c *mqlMicrosoftDevicemanagementDevicecompliancepolicy) GetProperties() *plugin.TValue[interface{}] {
	return &c.Properties
}

// mqlMicrosoftDevicemanagementSettings for the microsoft.devicemanagement.settings resource
type mqlMicrosoftDevicemanagementSettings struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMicrosoftDevicemanagementSettingsInternal it will be used here
	SecureByDefault plugin.TValue[bool]
	IsScheduledActionEnabled plugin.TValue[bool]
	DeviceComplianceCheckinThresholdDays plugin.TValue[int64]
}

// createMicrosoftDevicemanagementSettings creates a new instance of this resource
func createMicrosoftDevicemanagementSettings(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMicrosoftDevicemanagementSettings{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("microsoft.devicemanagement.settings", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMicrosoftDevicemanagementSettings) MqlName() string {
	return "microsoft.devicemanagement.settings"
}

func (c *mqlMicrosoftDevicemanagementSettings) MqlID() string {
	return c.__id
}

func (c *mqlMicrosoftDevicemanagementSettings) GetSecureByDefault() *plugin.TValue[bool] {
	return &c.SecureByDefault
}

func (c *mqlMicrosoftDevicemanagementSettings) GetIsScheduledActionEnabled() *plugin.TValue[bool] {
	return &c.IsScheduledActionEnabled
}

func (c *mqlMicrosoftDevicemanagementSettings) GetDeviceComplianceCheckinThresholdDays() *plugin.TValue[int64] {
	return &c.DeviceComplianceCheckinThresholdDays
}

// mqlMs365Exchangeonline for the ms365.exchangeonline resource
type mqlMs365Exchangeonline struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlMs365ExchangeonlineInternal
	MalwareFilterPolicy plugin.TValue[[]interface{}]
	HostedOutboundSpamFilterPolicy plugin.TValue[[]interface{}]
	TransportRule plugin.TValue[[]interface{}]
	RemoteDomain plugin.TValue[[]interface{}]
	SafeLinksPolicy plugin.TValue[[]interface{}]
	SafeAttachmentPolicy plugin.TValue[[]interface{}]
	OrganizationConfig plugin.TValue[interface{}]
	AuthenticationPolicy plugin.TValue[[]interface{}]
	AntiPhishPolicy plugin.TValue[[]interface{}]
	DkimSigningConfig plugin.TValue[[]interface{}]
	OwaMailboxPolicy plugin.TValue[[]interface{}]
	AdminAuditLogConfig plugin.TValue[interface{}]
	PhishFilterPolicy plugin.TValue[[]interface{}]
	Mailbox plugin.TValue[[]interface{}]
	AtpPolicyForO365 plugin.TValue[[]interface{}]
	SharingPolicy plugin.TValue[[]interface{}]
	RoleAssignmentPolicy plugin.TValue[[]interface{}]
	ExternalInOutlook plugin.TValue[[]interface{}]
	SharedMailboxes plugin.TValue[[]interface{}]
	TeamsProtectionPolicies plugin.TValue[[]interface{}]
	ReportSubmissionPolicies plugin.TValue[[]interface{}]
	MailboxesWithAudit plugin.TValue[[]interface{}]
	TransportConfig plugin.TValue[interface{}]
	SecurityAndCompliance plugin.TValue[*mqlMs365ExchangeonlineSecurityAndCompliance]
}

// createMs365Exchangeonline creates a new instance of this resource
func createMs365Exchangeonline(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365Exchangeonline{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.exchangeonline", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365Exchangeonline) MqlName() string {
	return "ms365.exchangeonline"
}

func (c *mqlMs365Exchangeonline) MqlID() string {
	return c.__id
}

func (c *mqlMs365Exchangeonline) GetMalwareFilterPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.MalwareFilterPolicy, func() ([]interface{}, error) {
		return c.malwareFilterPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetHostedOutboundSpamFilterPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.HostedOutboundSpamFilterPolicy, func() ([]interface{}, error) {
		return c.hostedOutboundSpamFilterPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetTransportRule() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.TransportRule, func() ([]interface{}, error) {
		return c.transportRule()
	})
}

func (c *mqlMs365Exchangeonline) GetRemoteDomain() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.RemoteDomain, func() ([]interface{}, error) {
		return c.remoteDomain()
	})
}

func (c *mqlMs365Exchangeonline) GetSafeLinksPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.SafeLinksPolicy, func() ([]interface{}, error) {
		return c.safeLinksPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetSafeAttachmentPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.SafeAttachmentPolicy, func() ([]interface{}, error) {
		return c.safeAttachmentPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetOrganizationConfig() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.OrganizationConfig, func() (interface{}, error) {
		return c.organizationConfig()
	})
}

func (c *mqlMs365Exchangeonline) GetAuthenticationPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.AuthenticationPolicy, func() ([]interface{}, error) {
		return c.authenticationPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetAntiPhishPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.AntiPhishPolicy, func() ([]interface{}, error) {
		return c.antiPhishPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetDkimSigningConfig() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.DkimSigningConfig, func() ([]interface{}, error) {
		return c.dkimSigningConfig()
	})
}

func (c *mqlMs365Exchangeonline) GetOwaMailboxPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.OwaMailboxPolicy, func() ([]interface{}, error) {
		return c.owaMailboxPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetAdminAuditLogConfig() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.AdminAuditLogConfig, func() (interface{}, error) {
		return c.adminAuditLogConfig()
	})
}

func (c *mqlMs365Exchangeonline) GetPhishFilterPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.PhishFilterPolicy, func() ([]interface{}, error) {
		return c.phishFilterPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetMailbox() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.Mailbox, func() ([]interface{}, error) {
		return c.mailbox()
	})
}

func (c *mqlMs365Exchangeonline) GetAtpPolicyForO365() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.AtpPolicyForO365, func() ([]interface{}, error) {
		return c.atpPolicyForO365()
	})
}

func (c *mqlMs365Exchangeonline) GetSharingPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.SharingPolicy, func() ([]interface{}, error) {
		return c.sharingPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetRoleAssignmentPolicy() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.RoleAssignmentPolicy, func() ([]interface{}, error) {
		return c.roleAssignmentPolicy()
	})
}

func (c *mqlMs365Exchangeonline) GetExternalInOutlook() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ExternalInOutlook, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.exchangeonline", c.__id, "externalInOutlook")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.externalInOutlook()
	})
}

func (c *mqlMs365Exchangeonline) GetSharedMailboxes() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.SharedMailboxes, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.exchangeonline", c.__id, "sharedMailboxes")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.sharedMailboxes()
	})
}

func (c *mqlMs365Exchangeonline) GetTeamsProtectionPolicies() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.TeamsProtectionPolicies, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.exchangeonline", c.__id, "teamsProtectionPolicies")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.teamsProtectionPolicies()
	})
}

func (c *mqlMs365Exchangeonline) GetReportSubmissionPolicies() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.ReportSubmissionPolicies, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.exchangeonline", c.__id, "reportSubmissionPolicies")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.reportSubmissionPolicies()
	})
}

func (c *mqlMs365Exchangeonline) GetMailboxesWithAudit() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.MailboxesWithAudit, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.exchangeonline", c.__id, "mailboxesWithAudit")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.mailboxesWithAudit()
	})
}

func (c *mqlMs365Exchangeonline) GetTransportConfig() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.TransportConfig, func() (interface{}, error) {
		return c.transportConfig()
	})
}

func (c *mqlMs365Exchangeonline) GetSecurityAndCompliance() *plugin.TValue[*mqlMs365ExchangeonlineSecurityAndCompliance] {
	return plugin.GetOrCompute[*mqlMs365ExchangeonlineSecurityAndCompliance](&c.SecurityAndCompliance, func() (*mqlMs365ExchangeonlineSecurityAndCompliance, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.exchangeonline", c.__id, "securityAndCompliance")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMs365ExchangeonlineSecurityAndCompliance), nil
			}
		}

		return c.securityAndCompliance()
	})
}

// mqlMs365ExchangeonlineSecurityAndCompliance for the ms365.exchangeonline.securityAndCompliance resource
type mqlMs365ExchangeonlineSecurityAndCompliance struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlMs365ExchangeonlineSecurityAndComplianceInternal
	DlpCompliancePolicies plugin.TValue[[]interface{}]
}

// createMs365ExchangeonlineSecurityAndCompliance creates a new instance of this resource
func createMs365ExchangeonlineSecurityAndCompliance(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365ExchangeonlineSecurityAndCompliance{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.exchangeonline.securityAndCompliance", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365ExchangeonlineSecurityAndCompliance) MqlName() string {
	return "ms365.exchangeonline.securityAndCompliance"
}

func (c *mqlMs365ExchangeonlineSecurityAndCompliance) MqlID() string {
	return c.__id
}

func (c *mqlMs365ExchangeonlineSecurityAndCompliance) GetDlpCompliancePolicies() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.DlpCompliancePolicies, func() ([]interface{}, error) {
		return c.dlpCompliancePolicies()
	})
}

// mqlMs365ExchangeonlineTeamsProtectionPolicy for the ms365.exchangeonline.teamsProtectionPolicy resource
type mqlMs365ExchangeonlineTeamsProtectionPolicy struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365ExchangeonlineTeamsProtectionPolicyInternal it will be used here
	ZapEnabled plugin.TValue[bool]
	IsValid plugin.TValue[bool]
}

// createMs365ExchangeonlineTeamsProtectionPolicy creates a new instance of this resource
func createMs365ExchangeonlineTeamsProtectionPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365ExchangeonlineTeamsProtectionPolicy{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.exchangeonline.teamsProtectionPolicy", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365ExchangeonlineTeamsProtectionPolicy) MqlName() string {
	return "ms365.exchangeonline.teamsProtectionPolicy"
}

func (c *mqlMs365ExchangeonlineTeamsProtectionPolicy) MqlID() string {
	return c.__id
}

func (c *mqlMs365ExchangeonlineTeamsProtectionPolicy) GetZapEnabled() *plugin.TValue[bool] {
	return &c.ZapEnabled
}

func (c *mqlMs365ExchangeonlineTeamsProtectionPolicy) GetIsValid() *plugin.TValue[bool] {
	return &c.IsValid
}

// mqlMs365ExchangeonlineReportSubmissionPolicy for the ms365.exchangeonline.reportSubmissionPolicy resource
type mqlMs365ExchangeonlineReportSubmissionPolicy struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365ExchangeonlineReportSubmissionPolicyInternal it will be used here
	ReportJunkToCustomizedAddress plugin.TValue[bool]
	ReportNotJunkToCustomizedAddress plugin.TValue[bool]
	ReportPhishToCustomizedAddress plugin.TValue[bool]
	ReportJunkAddresses plugin.TValue[[]interface{}]
	ReportNotJunkAddresses plugin.TValue[[]interface{}]
	ReportPhishAddresses plugin.TValue[[]interface{}]
	ReportChatMessageEnabled plugin.TValue[bool]
	ReportChatMessageToCustomizedAddressEnabled plugin.TValue[bool]
}

// createMs365ExchangeonlineReportSubmissionPolicy creates a new instance of this resource
func createMs365ExchangeonlineReportSubmissionPolicy(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365ExchangeonlineReportSubmissionPolicy{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.exchangeonline.reportSubmissionPolicy", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) MqlName() string {
	return "ms365.exchangeonline.reportSubmissionPolicy"
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) MqlID() string {
	return c.__id
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) GetReportJunkToCustomizedAddress() *plugin.TValue[bool] {
	return &c.ReportJunkToCustomizedAddress
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) GetReportNotJunkToCustomizedAddress() *plugin.TValue[bool] {
	return &c.ReportNotJunkToCustomizedAddress
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) GetReportPhishToCustomizedAddress() *plugin.TValue[bool] {
	return &c.ReportPhishToCustomizedAddress
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) GetReportJunkAddresses() *plugin.TValue[[]interface{}] {
	return &c.ReportJunkAddresses
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) GetReportNotJunkAddresses() *plugin.TValue[[]interface{}] {
	return &c.ReportNotJunkAddresses
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) GetReportPhishAddresses() *plugin.TValue[[]interface{}] {
	return &c.ReportPhishAddresses
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) GetReportChatMessageEnabled() *plugin.TValue[bool] {
	return &c.ReportChatMessageEnabled
}

func (c *mqlMs365ExchangeonlineReportSubmissionPolicy) GetReportChatMessageToCustomizedAddressEnabled() *plugin.TValue[bool] {
	return &c.ReportChatMessageToCustomizedAddressEnabled
}

// mqlMs365ExchangeonlineExternalSender for the ms365.exchangeonline.externalSender resource
type mqlMs365ExchangeonlineExternalSender struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365ExchangeonlineExternalSenderInternal it will be used here
	Identity plugin.TValue[string]
	AllowList plugin.TValue[[]interface{}]
	Enabled plugin.TValue[bool]
}

// createMs365ExchangeonlineExternalSender creates a new instance of this resource
func createMs365ExchangeonlineExternalSender(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365ExchangeonlineExternalSender{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.exchangeonline.externalSender", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365ExchangeonlineExternalSender) MqlName() string {
	return "ms365.exchangeonline.externalSender"
}

func (c *mqlMs365ExchangeonlineExternalSender) MqlID() string {
	return c.__id
}

func (c *mqlMs365ExchangeonlineExternalSender) GetIdentity() *plugin.TValue[string] {
	return &c.Identity
}

func (c *mqlMs365ExchangeonlineExternalSender) GetAllowList() *plugin.TValue[[]interface{}] {
	return &c.AllowList
}

func (c *mqlMs365ExchangeonlineExternalSender) GetEnabled() *plugin.TValue[bool] {
	return &c.Enabled
}

// mqlMs365ExchangeonlineExoMailbox for the ms365.exchangeonline.exoMailbox resource
type mqlMs365ExchangeonlineExoMailbox struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365ExchangeonlineExoMailboxInternal it will be used here
	Identity plugin.TValue[string]
	User plugin.TValue[*mqlMicrosoftUser]
	ExternalDirectoryObjectId plugin.TValue[string]
}

// createMs365ExchangeonlineExoMailbox creates a new instance of this resource
func createMs365ExchangeonlineExoMailbox(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365ExchangeonlineExoMailbox{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.exchangeonline.exoMailbox", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365ExchangeonlineExoMailbox) MqlName() string {
	return "ms365.exchangeonline.exoMailbox"
}

func (c *mqlMs365ExchangeonlineExoMailbox) MqlID() string {
	return c.__id
}

func (c *mqlMs365ExchangeonlineExoMailbox) GetIdentity() *plugin.TValue[string] {
	return &c.Identity
}

func (c *mqlMs365ExchangeonlineExoMailbox) GetUser() *plugin.TValue[*mqlMicrosoftUser] {
	return plugin.GetOrCompute[*mqlMicrosoftUser](&c.User, func() (*mqlMicrosoftUser, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.exchangeonline.exoMailbox", c.__id, "user")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMicrosoftUser), nil
			}
		}

		return c.user()
	})
}

func (c *mqlMs365ExchangeonlineExoMailbox) GetExternalDirectoryObjectId() *plugin.TValue[string] {
	return &c.ExternalDirectoryObjectId
}

// mqlMs365ExchangeonlineMailbox for the ms365.exchangeonline.mailbox resource
type mqlMs365ExchangeonlineMailbox struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365ExchangeonlineMailboxInternal it will be used here
	Identity plugin.TValue[string]
	DisplayName plugin.TValue[string]
	PrimarySmtpAddress plugin.TValue[string]
	RecipientTypeDetails plugin.TValue[string]
	AuditEnabled plugin.TValue[bool]
	AuditAdmin plugin.TValue[[]interface{}]
	AuditDelegate plugin.TValue[[]interface{}]
	AuditOwner plugin.TValue[[]interface{}]
	AuditLogAgeLimit plugin.TValue[string]
}

// createMs365ExchangeonlineMailbox creates a new instance of this resource
func createMs365ExchangeonlineMailbox(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365ExchangeonlineMailbox{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.exchangeonline.mailbox", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365ExchangeonlineMailbox) MqlName() string {
	return "ms365.exchangeonline.mailbox"
}

func (c *mqlMs365ExchangeonlineMailbox) MqlID() string {
	return c.__id
}

func (c *mqlMs365ExchangeonlineMailbox) GetIdentity() *plugin.TValue[string] {
	return &c.Identity
}

func (c *mqlMs365ExchangeonlineMailbox) GetDisplayName() *plugin.TValue[string] {
	return &c.DisplayName
}

func (c *mqlMs365ExchangeonlineMailbox) GetPrimarySmtpAddress() *plugin.TValue[string] {
	return &c.PrimarySmtpAddress
}

func (c *mqlMs365ExchangeonlineMailbox) GetRecipientTypeDetails() *plugin.TValue[string] {
	return &c.RecipientTypeDetails
}

func (c *mqlMs365ExchangeonlineMailbox) GetAuditEnabled() *plugin.TValue[bool] {
	return &c.AuditEnabled
}

func (c *mqlMs365ExchangeonlineMailbox) GetAuditAdmin() *plugin.TValue[[]interface{}] {
	return &c.AuditAdmin
}

func (c *mqlMs365ExchangeonlineMailbox) GetAuditDelegate() *plugin.TValue[[]interface{}] {
	return &c.AuditDelegate
}

func (c *mqlMs365ExchangeonlineMailbox) GetAuditOwner() *plugin.TValue[[]interface{}] {
	return &c.AuditOwner
}

func (c *mqlMs365ExchangeonlineMailbox) GetAuditLogAgeLimit() *plugin.TValue[string] {
	return &c.AuditLogAgeLimit
}

// mqlMs365Sharepointonline for the ms365.sharepointonline resource
type mqlMs365Sharepointonline struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlMs365SharepointonlineInternal
	SpoTenant plugin.TValue[interface{}]
	SpoTenantSyncClientRestriction plugin.TValue[interface{}]
	SpoSites plugin.TValue[[]interface{}]
	DefaultLinkPermission plugin.TValue[string]
}

// createMs365Sharepointonline creates a new instance of this resource
func createMs365Sharepointonline(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365Sharepointonline{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.sharepointonline", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365Sharepointonline) MqlName() string {
	return "ms365.sharepointonline"
}

func (c *mqlMs365Sharepointonline) MqlID() string {
	return c.__id
}

func (c *mqlMs365Sharepointonline) GetSpoTenant() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.SpoTenant, func() (interface{}, error) {
		return c.spoTenant()
	})
}

func (c *mqlMs365Sharepointonline) GetSpoTenantSyncClientRestriction() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.SpoTenantSyncClientRestriction, func() (interface{}, error) {
		return c.spoTenantSyncClientRestriction()
	})
}

func (c *mqlMs365Sharepointonline) GetSpoSites() *plugin.TValue[[]interface{}] {
	return plugin.GetOrCompute[[]interface{}](&c.SpoSites, func() ([]interface{}, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.sharepointonline", c.__id, "spoSites")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.([]interface{}), nil
			}
		}

		return c.spoSites()
	})
}

func (c *mqlMs365Sharepointonline) GetDefaultLinkPermission() *plugin.TValue[string] {
	return plugin.GetOrCompute[string](&c.DefaultLinkPermission, func() (string, error) {
		return c.defaultLinkPermission()
	})
}

// mqlMs365SharepointonlineSite for the ms365.sharepointonline.site resource
type mqlMs365SharepointonlineSite struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365SharepointonlineSiteInternal it will be used here
	Url plugin.TValue[string]
	DenyAddAndCustomizePages plugin.TValue[bool]
}

// createMs365SharepointonlineSite creates a new instance of this resource
func createMs365SharepointonlineSite(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365SharepointonlineSite{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	if res.__id == "" {
	res.__id, err = res.id()
		if err != nil {
			return nil, err
		}
	}

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.sharepointonline.site", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365SharepointonlineSite) MqlName() string {
	return "ms365.sharepointonline.site"
}

func (c *mqlMs365SharepointonlineSite) MqlID() string {
	return c.__id
}

func (c *mqlMs365SharepointonlineSite) GetUrl() *plugin.TValue[string] {
	return &c.Url
}

func (c *mqlMs365SharepointonlineSite) GetDenyAddAndCustomizePages() *plugin.TValue[bool] {
	return &c.DenyAddAndCustomizePages
}

// mqlMs365Teams for the ms365.teams resource
type mqlMs365Teams struct {
	MqlRuntime *plugin.Runtime
	__id string
	mqlMs365TeamsInternal
	CsTeamsClientConfiguration plugin.TValue[interface{}]
	CsTenantFederationConfiguration plugin.TValue[*mqlMs365TeamsTenantFederationConfig]
	CsTeamsMeetingPolicy plugin.TValue[*mqlMs365TeamsTeamsMeetingPolicyConfig]
	CsTeamsMessagingPolicy plugin.TValue[*mqlMs365TeamsTeamsMessagingPolicyConfig]
}

// createMs365Teams creates a new instance of this resource
func createMs365Teams(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365Teams{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.teams", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365Teams) MqlName() string {
	return "ms365.teams"
}

func (c *mqlMs365Teams) MqlID() string {
	return c.__id
}

func (c *mqlMs365Teams) GetCsTeamsClientConfiguration() *plugin.TValue[interface{}] {
	return plugin.GetOrCompute[interface{}](&c.CsTeamsClientConfiguration, func() (interface{}, error) {
		return c.csTeamsClientConfiguration()
	})
}

func (c *mqlMs365Teams) GetCsTenantFederationConfiguration() *plugin.TValue[*mqlMs365TeamsTenantFederationConfig] {
	return plugin.GetOrCompute[*mqlMs365TeamsTenantFederationConfig](&c.CsTenantFederationConfiguration, func() (*mqlMs365TeamsTenantFederationConfig, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.teams", c.__id, "csTenantFederationConfiguration")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMs365TeamsTenantFederationConfig), nil
			}
		}

		return c.csTenantFederationConfiguration()
	})
}

func (c *mqlMs365Teams) GetCsTeamsMeetingPolicy() *plugin.TValue[*mqlMs365TeamsTeamsMeetingPolicyConfig] {
	return plugin.GetOrCompute[*mqlMs365TeamsTeamsMeetingPolicyConfig](&c.CsTeamsMeetingPolicy, func() (*mqlMs365TeamsTeamsMeetingPolicyConfig, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.teams", c.__id, "csTeamsMeetingPolicy")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMs365TeamsTeamsMeetingPolicyConfig), nil
			}
		}

		return c.csTeamsMeetingPolicy()
	})
}

func (c *mqlMs365Teams) GetCsTeamsMessagingPolicy() *plugin.TValue[*mqlMs365TeamsTeamsMessagingPolicyConfig] {
	return plugin.GetOrCompute[*mqlMs365TeamsTeamsMessagingPolicyConfig](&c.CsTeamsMessagingPolicy, func() (*mqlMs365TeamsTeamsMessagingPolicyConfig, error) {
		if c.MqlRuntime.HasRecording {
			d, err := c.MqlRuntime.FieldResourceFromRecording("ms365.teams", c.__id, "csTeamsMessagingPolicy")
			if err != nil {
				return nil, err
			}
			if d != nil {
				return d.Value.(*mqlMs365TeamsTeamsMessagingPolicyConfig), nil
			}
		}

		return c.csTeamsMessagingPolicy()
	})
}

// mqlMs365TeamsTenantFederationConfig for the ms365.teams.tenantFederationConfig resource
type mqlMs365TeamsTenantFederationConfig struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365TeamsTenantFederationConfigInternal it will be used here
	Identity plugin.TValue[string]
	BlockedDomains plugin.TValue[interface{}]
	AllowedDomains plugin.TValue[[]interface{}]
	AllowFederatedUsers plugin.TValue[bool]
	AllowPublicUsers plugin.TValue[bool]
	AllowTeamsConsumer plugin.TValue[bool]
	AllowTeamsConsumerInbound plugin.TValue[bool]
	TreatDiscoveredPartnersAsUnverified plugin.TValue[bool]
	SharedSipAddressSpace plugin.TValue[bool]
	RestrictTeamsConsumerToExternalUserProfiles plugin.TValue[bool]
}

// createMs365TeamsTenantFederationConfig creates a new instance of this resource
func createMs365TeamsTenantFederationConfig(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365TeamsTenantFederationConfig{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.teams.tenantFederationConfig", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365TeamsTenantFederationConfig) MqlName() string {
	return "ms365.teams.tenantFederationConfig"
}

func (c *mqlMs365TeamsTenantFederationConfig) MqlID() string {
	return c.__id
}

func (c *mqlMs365TeamsTenantFederationConfig) GetIdentity() *plugin.TValue[string] {
	return &c.Identity
}

func (c *mqlMs365TeamsTenantFederationConfig) GetBlockedDomains() *plugin.TValue[interface{}] {
	return &c.BlockedDomains
}

func (c *mqlMs365TeamsTenantFederationConfig) GetAllowedDomains() *plugin.TValue[[]interface{}] {
	return &c.AllowedDomains
}

func (c *mqlMs365TeamsTenantFederationConfig) GetAllowFederatedUsers() *plugin.TValue[bool] {
	return &c.AllowFederatedUsers
}

func (c *mqlMs365TeamsTenantFederationConfig) GetAllowPublicUsers() *plugin.TValue[bool] {
	return &c.AllowPublicUsers
}

func (c *mqlMs365TeamsTenantFederationConfig) GetAllowTeamsConsumer() *plugin.TValue[bool] {
	return &c.AllowTeamsConsumer
}

func (c *mqlMs365TeamsTenantFederationConfig) GetAllowTeamsConsumerInbound() *plugin.TValue[bool] {
	return &c.AllowTeamsConsumerInbound
}

func (c *mqlMs365TeamsTenantFederationConfig) GetTreatDiscoveredPartnersAsUnverified() *plugin.TValue[bool] {
	return &c.TreatDiscoveredPartnersAsUnverified
}

func (c *mqlMs365TeamsTenantFederationConfig) GetSharedSipAddressSpace() *plugin.TValue[bool] {
	return &c.SharedSipAddressSpace
}

func (c *mqlMs365TeamsTenantFederationConfig) GetRestrictTeamsConsumerToExternalUserProfiles() *plugin.TValue[bool] {
	return &c.RestrictTeamsConsumerToExternalUserProfiles
}

// mqlMs365TeamsTeamsMeetingPolicyConfig for the ms365.teams.teamsMeetingPolicyConfig resource
type mqlMs365TeamsTeamsMeetingPolicyConfig struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365TeamsTeamsMeetingPolicyConfigInternal it will be used here
	AllowAnonymousUsersToJoinMeeting plugin.TValue[bool]
	AllowAnonymousUsersToStartMeeting plugin.TValue[bool]
	AllowExternalNonTrustedMeetingChat plugin.TValue[bool]
	AutoAdmittedUsers plugin.TValue[string]
	AllowPSTNUsersToBypassLobby plugin.TValue[bool]
	MeetingChatEnabledType plugin.TValue[string]
	DesignatedPresenterRoleMode plugin.TValue[string]
	AllowExternalParticipantGiveRequestControl plugin.TValue[bool]
	AllowSecurityEndUserReporting plugin.TValue[bool]
	AllowCloudRecordingForCalls plugin.TValue[bool]
}

// createMs365TeamsTeamsMeetingPolicyConfig creates a new instance of this resource
func createMs365TeamsTeamsMeetingPolicyConfig(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365TeamsTeamsMeetingPolicyConfig{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.teams.teamsMeetingPolicyConfig", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) MqlName() string {
	return "ms365.teams.teamsMeetingPolicyConfig"
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) MqlID() string {
	return c.__id
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetAllowAnonymousUsersToJoinMeeting() *plugin.TValue[bool] {
	return &c.AllowAnonymousUsersToJoinMeeting
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetAllowAnonymousUsersToStartMeeting() *plugin.TValue[bool] {
	return &c.AllowAnonymousUsersToStartMeeting
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetAllowExternalNonTrustedMeetingChat() *plugin.TValue[bool] {
	return &c.AllowExternalNonTrustedMeetingChat
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetAutoAdmittedUsers() *plugin.TValue[string] {
	return &c.AutoAdmittedUsers
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetAllowPSTNUsersToBypassLobby() *plugin.TValue[bool] {
	return &c.AllowPSTNUsersToBypassLobby
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetMeetingChatEnabledType() *plugin.TValue[string] {
	return &c.MeetingChatEnabledType
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetDesignatedPresenterRoleMode() *plugin.TValue[string] {
	return &c.DesignatedPresenterRoleMode
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetAllowExternalParticipantGiveRequestControl() *plugin.TValue[bool] {
	return &c.AllowExternalParticipantGiveRequestControl
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetAllowSecurityEndUserReporting() *plugin.TValue[bool] {
	return &c.AllowSecurityEndUserReporting
}

func (c *mqlMs365TeamsTeamsMeetingPolicyConfig) GetAllowCloudRecordingForCalls() *plugin.TValue[bool] {
	return &c.AllowCloudRecordingForCalls
}

// mqlMs365TeamsTeamsMessagingPolicyConfig for the ms365.teams.teamsMessagingPolicyConfig resource
type mqlMs365TeamsTeamsMessagingPolicyConfig struct {
	MqlRuntime *plugin.Runtime
	__id string
	// optional: if you define mqlMs365TeamsTeamsMessagingPolicyConfigInternal it will be used here
	AllowSecurityEndUserReporting plugin.TValue[bool]
}

// createMs365TeamsTeamsMessagingPolicyConfig creates a new instance of this resource
func createMs365TeamsTeamsMessagingPolicyConfig(runtime *plugin.Runtime, args map[string]*llx.RawData) (plugin.Resource, error) {
	res := &mqlMs365TeamsTeamsMessagingPolicyConfig{
		MqlRuntime: runtime,
	}

	err := SetAllData(res, args)
	if err != nil {
		return res, err
	}

	// to override __id implement: id() (string, error)

	if runtime.HasRecording {
		args, err = runtime.ResourceFromRecording("ms365.teams.teamsMessagingPolicyConfig", res.__id)
		if err != nil || args == nil {
			return res, err
		}
		return res, SetAllData(res, args)
	}

	return res, nil
}

func (c *mqlMs365TeamsTeamsMessagingPolicyConfig) MqlName() string {
	return "ms365.teams.teamsMessagingPolicyConfig"
}

func (c *mqlMs365TeamsTeamsMessagingPolicyConfig) MqlID() string {
	return c.__id
}

func (c *mqlMs365TeamsTeamsMessagingPolicyConfig) GetAllowSecurityEndUserReporting() *plugin.TValue[bool] {
	return &c.AllowSecurityEndUserReporting
}
